import 'dart:convert';

class ValidationResponse {
  final bool success;
  final ValidationData data;

  ValidationResponse({
    required this.success,
    required this.data,
  });

  factory ValidationResponse.fromJson(Map<String, dynamic> json) {
    // Handle nested data structure from backend
    final dataJson = json['data'] ?? {};
    final actualData = dataJson['data'] ?? dataJson; // Handle double-nested data

    return ValidationResponse(
      success: json['success'] ?? false,
      data: ValidationData.fromJson(actualData),
    );
  }

  factory ValidationResponse.fromRawJson(String str) =>
      ValidationResponse.fromJson(json.decode(str));

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.toJson(),
    };
  }

  String toRawJson() => json.encode(toJson());

  @override
  String toString() {
    return 'ValidationResponse(success: $success, data: $data)';
  }
}

class ValidationData {
  final ValidationResult validationResult;
  final RouteAnalysis routeAnalysis;
  final List<DocumentValidation> documentValidations;
  final NextSteps nextSteps;

  ValidationData({
    required this.validationResult,
    required this.routeAnalysis,
    required this.documentValidations,
    required this.nextSteps,
  });

  factory ValidationData.fromJson(Map<String, dynamic> json) {
    return ValidationData(
      validationResult: ValidationResult.fromJson(json['validation_result'] ?? {}),
      routeAnalysis: RouteAnalysis.fromJson(json['route_analysis'] ?? {}),
      documentValidations: (json['document_validations'] as List<dynamic>?)
              ?.map((item) => DocumentValidation.fromJson(item))
              .toList() ??
          [],
      nextSteps: NextSteps.fromJson(json['next_steps'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'validation_result': validationResult.toJson(),
      'route_analysis': routeAnalysis.toJson(),
      'document_validations': documentValidations.map((doc) => doc.toJson()).toList(),
      'next_steps': nextSteps.toJson(),
    };
  }

  @override
  String toString() {
    return 'ValidationData(validationResult: $validationResult, routeAnalysis: $routeAnalysis)';
  }
}

class ValidationResult {
  final bool isValid;
  final bool routeCompleted;
  final bool canProceed;
  final List<String> errors;

  ValidationResult({
    required this.isValid,
    required this.routeCompleted,
    required this.canProceed,
    this.errors = const [],
  });

  factory ValidationResult.fromJson(Map<String, dynamic> json) {
    return ValidationResult(
      isValid: json['is_valid'] ?? false,
      routeCompleted: json['route_completed'] ?? false,
      canProceed: json['can_proceed'] ?? false,
      errors: List<String>.from(json['errors'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'is_valid': isValid,
      'route_completed': routeCompleted,
      'can_proceed': canProceed,
      'errors': errors,
    };
  }

  @override
  String toString() {
    return 'ValidationResult(isValid: $isValid, routeCompleted: $routeCompleted, canProceed: $canProceed, errors: $errors)';
  }
}

class RouteAnalysis {
  final Map<String, RequirementStatus> requirementsMet;

  RouteAnalysis({
    required this.requirementsMet,
  });

  factory RouteAnalysis.fromJson(Map<String, dynamic> json) {
    final requirementsMetMap = <String, RequirementStatus>{};
    final requirementsMetJson = json['requirements_met'] as Map<String, dynamic>? ?? {};
    
    for (final entry in requirementsMetJson.entries) {
      requirementsMetMap[entry.key] = RequirementStatus.fromJson(entry.value);
    }

    return RouteAnalysis(
      requirementsMet: requirementsMetMap,
    );
  }

  Map<String, dynamic> toJson() {
    final requirementsMetJson = <String, dynamic>{};
    for (final entry in requirementsMet.entries) {
      requirementsMetJson[entry.key] = entry.value.toJson();
    }

    return {
      'requirements_met': requirementsMetJson,
    };
  }

  @override
  String toString() {
    return 'RouteAnalysis(requirementsMet: $requirementsMet)';
  }
}

class RequirementStatus {
  final int required;
  final int provided;
  final bool met;

  RequirementStatus({
    required this.required,
    required this.provided,
    required this.met,
  });

  factory RequirementStatus.fromJson(Map<String, dynamic> json) {
    // Handle both boolean and integer values for required/provided fields
    final requiredValue = json['required'];
    final providedValue = json['provided'];

    return RequirementStatus(
      required: _convertToInt(requiredValue),
      provided: _convertToInt(providedValue),
      met: json['met'] ?? false,
    );
  }

  static int _convertToInt(dynamic value) {
    if (value is int) return value;
    if (value is bool) return value ? 1 : 0;
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  Map<String, dynamic> toJson() {
    return {
      'required': required,
      'provided': provided,
      'met': met,
    };
  }

  @override
  String toString() {
    return 'RequirementStatus(required: $required, provided: $provided, met: $met)';
  }
}

class DocumentValidation {
  final int documentTypeId;
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  DocumentValidation({
    required this.documentTypeId,
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  factory DocumentValidation.fromJson(Map<String, dynamic> json) {
    return DocumentValidation(
      documentTypeId: json['document_type_id'] ?? 0,
      isValid: json['is_valid'] ?? false,
      errors: List<String>.from(json['errors'] ?? []),
      warnings: List<String>.from(json['warnings'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'document_type_id': documentTypeId,
      'is_valid': isValid,
      'errors': errors,
      'warnings': warnings,
    };
  }

  @override
  String toString() {
    return 'DocumentValidation(documentTypeId: $documentTypeId, isValid: $isValid, errors: $errors, warnings: $warnings)';
  }
}

class NextSteps {
  final String action;
  final String message;

  NextSteps({
    required this.action,
    required this.message,
  });

  factory NextSteps.fromJson(Map<String, dynamic> json) {
    return NextSteps(
      action: json['action'] ?? '',
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'action': action,
      'message': message,
    };
  }

  @override
  String toString() {
    return 'NextSteps(action: $action, message: $message)';
  }
}
