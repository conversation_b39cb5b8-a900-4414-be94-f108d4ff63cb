import 'dart:io';
import 'package:dio/dio.dart';

class DocumentUploadApiService {
  final Dio _dio;

  DocumentUploadApiService(this._dio) {
    _dio.options.baseUrl = 'http://localhost:8001/api/v1';
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);

    // Add logging interceptor
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print('🌐 Upload API: $obj'),
    ));
  }

  Future<Map<String, dynamic>> uploadDocumentFile({
    required String token,
    required String applicationId,
    required String nominationId,
    required File file,
    String? fileName,
  }) async {
    try {
      print('🚀 Starting file upload to API');
      print('📋 Upload details: applicationId=$applicationId, nominationId=$nominationId');

      // Read file bytes for web compatibility
      final fileBytes = await file.readAsBytes();
      final finalFileName = fileName ?? 'document.pdf';

      print('📁 File: $finalFileName, size: ${fileBytes.length} bytes');

      final formData = FormData.fromMap({
        'file': MultipartFile.fromBytes(
          fileBytes,
          filename: finalFileName,
        ),
      });

      print('📤 Uploading to: /applications/$applicationId/nominations/$nominationId/upload');

      final response = await _dio.post(
        '/applications/$applicationId/nominations/$nominationId/upload',
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      print('✅ Upload response: ${response.statusCode}');
      print('📥 Response data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        throw Exception('Failed to upload document file: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('❌ Dio error during upload: ${e.message}');
      print('❌ Response: ${e.response?.data}');
      throw _handleDioError(e);
    } catch (error) {
      print('❌ General error during upload: $error');
      throw Exception('Failed to upload document file: ${error.toString()}');
    }
  }

  Future<Map<String, dynamic>> getDocumentFiles({
    required String token,
    required String applicationId,
    required String nominationId,
  }) async {
    try {
      final response = await _dio.get(
        '/applications/$applicationId/nominations/$nominationId/files',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to get document files: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to get document files: ${error.toString()}');
    }
  }

  Future<void> deleteDocumentFile({
    required String token,
    required String applicationId,
    required String nominationId,
    required String fileId,
  }) async {
    try {
      final response = await _dio.delete(
        '/applications/$applicationId/nominations/$nominationId/files/$fileId',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to delete document file: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to delete document file: ${error.toString()}');
    }
  }

  Exception _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception('Connection timeout. Please check your internet connection.');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Server error';
        return Exception('Server error ($statusCode): $message');
      case DioExceptionType.cancel:
        return Exception('Request was cancelled');
      case DioExceptionType.unknown:
      default:
        return Exception('Network error: ${e.message}');
    }
  }
}
