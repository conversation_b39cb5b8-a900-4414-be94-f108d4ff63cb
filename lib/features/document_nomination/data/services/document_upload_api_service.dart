import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';

class DocumentUploadApiService {
  final Dio _dio;

  DocumentUploadApiService(this._dio) {
    _dio.options.baseUrl = 'http://localhost:8001/api/v1';
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);

    // Add logging interceptor
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print('🌐 Upload API: $obj'),
    ));
  }

  Future<Map<String, dynamic>> uploadDocumentFile({
    required String token,
    required String applicationId,
    required String nominationId,
    required File file,
    String? fileName,
    Uint8List? fileBytes,
  }) async {
    try {
      print('🚀 [UPLOAD] Starting file upload to API');
      print('📋 [UPLOAD] Upload details: applicationId=$applicationId, nominationId=$nominationId');
      print('🔑 [UPLOAD] Token: ${token.substring(0, 20)}...');

      // Use provided file bytes or read from file
      Uint8List actualFileBytes;
      if (fileBytes != null) {
        print('📖 [UPLOAD] Using provided file bytes...');
        actualFileBytes = fileBytes;
      } else {
        print('📖 [UPLOAD] Reading file bytes from File object...');
        actualFileBytes = await file.readAsBytes();
      }

      final finalFileName = fileName ?? 'document.pdf';

      print('📁 [UPLOAD] File details:');
      print('   - Name: $finalFileName');
      print('   - Size: ${actualFileBytes.length} bytes');
      print('   - First 10 bytes: ${actualFileBytes.take(10).toList()}');

      print('🔧 [UPLOAD] Creating FormData...');
      final formData = FormData.fromMap({
        'file': MultipartFile.fromBytes(
          actualFileBytes,
          filename: finalFileName,
        ),
      });
      print('✅ [UPLOAD] FormData created successfully');

      final uploadUrl = '/applications/$applicationId/nominations/$nominationId/upload';
      print('📤 [UPLOAD] Uploading to: $uploadUrl');
      print('📤 [UPLOAD] Full URL: ${_dio.options.baseUrl}$uploadUrl');

      print('🌐 [UPLOAD] Making POST request...');
      final response = await _dio.post(
        uploadUrl,
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      print('✅ [UPLOAD] Upload response received:');
      print('   - Status Code: ${response.statusCode}');
      print('   - Headers: ${response.headers}');
      print('📥 [UPLOAD] Response data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('🎉 [UPLOAD] Upload completed successfully!');
        return response.data;
      } else {
        print('❌ [UPLOAD] Upload failed with status code: ${response.statusCode}');
        print('❌ [UPLOAD] Response body: ${response.data}');
        throw Exception('Failed to upload document file: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('❌ [UPLOAD] DioException during upload:');
      print('   - Type: ${e.type}');
      print('   - Message: ${e.message}');
      print('   - Status Code: ${e.response?.statusCode}');
      print('   - Response Headers: ${e.response?.headers}');
      print('   - Response Data: ${e.response?.data}');
      print('   - Request Path: ${e.requestOptions.path}');
      print('   - Request Headers: ${e.requestOptions.headers}');
      print('   - Stack Trace: ${e.stackTrace}');
      throw _handleDioError(e);
    } catch (error, stackTrace) {
      print('❌ [UPLOAD] General error during upload:');
      print('   - Error: $error');
      print('   - Type: ${error.runtimeType}');
      print('   - Stack Trace: $stackTrace');
      throw Exception('Failed to upload document file: ${error.toString()}');
    }
  }

  Future<Map<String, dynamic>> getDocumentFiles({
    required String token,
    required String applicationId,
    required String nominationId,
  }) async {
    try {
      final response = await _dio.get(
        '/applications/$applicationId/nominations/$nominationId/files',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to get document files: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to get document files: ${error.toString()}');
    }
  }

  Future<void> deleteDocumentFile({
    required String token,
    required String applicationId,
    required String nominationId,
    required String fileId,
  }) async {
    try {
      final response = await _dio.delete(
        '/applications/$applicationId/nominations/$nominationId/files/$fileId',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to delete document file: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to delete document file: ${error.toString()}');
    }
  }

  Exception _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception('Connection timeout. Please check your internet connection.');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Server error';
        return Exception('Server error ($statusCode): $message');
      case DioExceptionType.cancel:
        return Exception('Request was cancelled');
      case DioExceptionType.unknown:
      default:
        return Exception('Network error: ${e.message}');
    }
  }
}
