import 'dart:io';

import 'package:SolidCheck/core/config/design_config.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/data/services/document_upload_api_service.dart';
import 'package:SolidCheck/features/document_nomination/presentation/providers/document_nomination_provider.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/document_upload_widget.dart';
import 'package:dio/dio.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/enhanced_form_fields.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_document_service.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_name_validation_service.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_postcode_service.dart';
import 'package:SolidCheck/shared/widgets/custom_snakbar.dart';
import 'package:SolidCheck/shared/widgets/navigation/applicant_sidebar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:intl/intl.dart';

class DocumentDetailScreen extends ConsumerStatefulWidget {
  final String applicationId;
  final String applicantName;
  final DocumentType documentType;
  final DocumentNomination? existingNomination;
  final String? applicantId;

  const DocumentDetailScreen({
    super.key,
    required this.applicationId,
    required this.applicantName,
    required this.documentType,
    this.existingNomination,
    this.applicantId,
  });

  @override
  ConsumerState<DocumentDetailScreen> createState() => _DocumentDetailScreenState();
}

class _DocumentDetailScreenState extends ConsumerState<DocumentDetailScreen> {
  final _formKey = GlobalKey<FormState>();
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, String?> _errors = {};
  final Map<String, dynamic> _enhancedFieldValues = {};
  bool _confirmsAddress = false;
  File? _uploadedFile;
  ApplicantDetailsData? _applicantData;

  Widget _buildSidebar() {
    final authState = ref.read(authViewModelProvider);
    final userType = authState.authResult?.user?.userType;

    if (userType == 'applicant') {
      // For applicant users, use the ApplicantSidebar with their own user ID
      final userId = authState.authResult?.user?.id?.toString();
      return ApplicantSidebar(
        applicantId: userId,
        showBackButton: true,
        onBackPressed: () => Navigator.of(context).pop(),
      );
    } else {
      // For client users, use a simplified sidebar without applicant-specific data
      return _buildClientSidebar();
    }
  }

  Widget _buildClientSidebar() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'Document Details',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  widget.applicantName,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          // Navigation items
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                _buildSidebarItem(
                  icon: Icons.dashboard,
                  title: 'Dashboard',
                  isActive: false,
                  onTap: () => _navigateBackToDashboard(),
                ),
                _buildSidebarItem(
                  icon: Icons.description,
                  title: 'Document Nomination',
                  isActive: false,
                  onTap: () => Navigator.of(context).pop(),
                ),
                _buildSidebarItem(
                  icon: Icons.info,
                  title: 'Document Details',
                  isActive: true,
                  onTap: () {},
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSidebarItem({
    required IconData icon,
    required String title,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: isActive ? Theme.of(context).primaryColor : Colors.grey[600],
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isActive ? Theme.of(context).primaryColor : Colors.grey[800],
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        selected: isActive,
        selectedTileColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        onTap: onTap,
      ),
    );
  }

  void _navigateBackToDashboard() {
    final authState = ref.read(authViewModelProvider);
    final userType = authState.authResult?.user?.userType;

    if (userType == 'client_user') {
      Navigator.of(context).pushNamedAndRemoveUntil(
        '/client-dashboard',
        (route) => false,
      );
    } else {
      Navigator.of(context).pushNamedAndRemoveUntil(
        '/dashboard',
        (route) => false,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadExistingData();
    _loadApplicantDataFromProvider();
    _loadExistingFile();
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _initializeControllers() {
    for (final field in widget.documentType.dataFields) {
      _controllers[field.name] = TextEditingController();
    }
  }

  void _loadExistingData() {
    if (widget.existingNomination != null) {
      final data = widget.existingNomination!.documentData;
      for (final field in widget.documentType.dataFields) {
        if (data.containsKey(field.name)) {
          _controllers[field.name]?.text = data[field.name]?.toString() ?? '';
        }

        // Load enhanced field responses if available
        final enhancedKey = '${field.name}_enhanced_response';
        if (data.containsKey(enhancedKey)) {
          _enhancedFieldValues[field.name] = data[enhancedKey];
        }
      }
      _confirmsAddress = widget.existingNomination!.confirmsAddress;
    }
  }

  void _loadApplicantDataFromProvider() {
    final documentNominationState = ref.read(documentNominationProvider);

    if (documentNominationState.applicantData != null) {
      setState(() {
        _applicantData = documentNominationState.applicantData;
      });
    } else {
    }
  }

  void _loadExistingFile() {
    final notifier = ref.read(documentNominationProvider.notifier);
    final existingFile = notifier.getUploadedFile(widget.documentType.id);
    if (existingFile != null) {
      setState(() {
        _uploadedFile = existingFile;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen to provider changes and update applicant data if needed
    ref.listen<DocumentNominationState>(documentNominationProvider, (previous, next) {
      if (next.applicantData != null && _applicantData == null) {
        setState(() {
          _applicantData = next.applicantData;
        });
      }
    });

    final isMobile = context.isMobile;
    final horizontalPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 16.0,
      tablet: 24.0,
      desktop: 32.0,
    );

    return Scaffold(
      backgroundColor: Colors.white,
      body: Row(
        children: [
          if (!isMobile)
            SizedBox(
              width: 280,
              child: ApplicantSidebar(
                applicantId: widget.applicantId,
                showBackButton: true,
                onBackPressed: () => Navigator.of(context).pop(),
              ),
            ),
          Expanded(
            child: Container(
              color: Colors.white,
              child: Column(
                children: [
                  _buildHeader(context, horizontalPadding),
                  Expanded(
                    child: _buildContent(context, horizontalPadding),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, double horizontalPadding) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 16,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.arrow_back,
              color: DesignConfig.primaryColor,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Document Details',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 20.0,
                      tablet: 22.0,
                      desktop: 24.0,
                    ),
                    fontWeight: FontWeight.bold,
                    color: DesignConfig.primaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.documentType.name,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 14.0,
                      tablet: 15.0,
                      desktop: 16.0,
                    ),
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, double horizontalPadding) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 24),
          _buildDocumentInfo(),
          const SizedBox(height: 24),
          _buildForm(),
          const SizedBox(height: 24),
          if (_documentRequiresPhoto()) _buildUploadSection(),
          if (_documentRequiresPhoto()) const SizedBox(height: 24),
          _buildAddressConfirmationSection(),
          const SizedBox(height: 32),
          _buildActionButtons(),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildDocumentInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: DesignConfig.primaryColor,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              _getDocumentIcon(),
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.documentType.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                if (widget.documentType.requiresPhoto)
                  Text(
                    'Photo required',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                if (widget.documentType.confirmsAddress)
                  Text(
                    'Confirms address',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getDocumentIcon() {
    final name = widget.documentType.name.toLowerCase();
    if (name.contains('passport')) return Icons.book;
    if (name.contains('licence') || name.contains('license')) return Icons.credit_card;
    if (name.contains('certificate')) return Icons.verified;
    if (name.contains('statement')) return Icons.description;
    if (name.contains('card')) return Icons.credit_card;
    return Icons.description;
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Document Information',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          ...widget.documentType.dataFields.map((field) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: _buildFormField(field.name, field),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildFormField(String fieldName, DocumentDataField field) {
    final enhancedType = field.enhancedType;

    // Use enhanced field components for better UX
    switch (enhancedType) {
      case 'yes_no_confirmation':
        return _buildYesNoConfirmationField(fieldName, field);
      case 'multiple_choice_postcode':
        return _buildMultipleChoicePostcodeField(fieldName, field);
      case 'smart_date':
        return _buildSmartDateField(fieldName, field);
      case 'country_dropdown':
        return _buildCountryDropdownField(fieldName, field);
      default:
        return _buildTraditionalField(fieldName, field);
    }
  }

  Widget _buildYesNoConfirmationField(String fieldName, DocumentDataField field) {
    final confirmationText = SmartNameValidationService.generateNameConfirmationText(
      fieldName,
      _applicantData,
    );

    return YesNoConfirmationField(
      field: field,
      initialValue: _enhancedFieldValues[fieldName],
      confirmationText: confirmationText,
      errorText: _errors[fieldName],
      onChanged: (value) {
        setState(() {
          _enhancedFieldValues[fieldName] = value;
          _errors[fieldName] = null;

          // Set the expected value in the controller for form submission
          if (value == 'yes') {
            final expectedValue = SmartNameValidationService.getExpectedNameValue(
              fieldName,
              _applicantData,
            );
            _controllers[fieldName]?.text = expectedValue;
          }
        });
      },
    );
  }

  Widget _buildMultipleChoicePostcodeField(String fieldName, DocumentDataField field) {
    final postcodeOptions = SmartPostcodeService.generatePostcodeOptions(
      _applicantData?.currentAddress.postcode ?? 'LE7 7AQ',
      _applicantData,
    );

    return MultipleChoicePostcodeField(
      field: field,
      initialValue: _enhancedFieldValues[fieldName],
      postcodeOptions: postcodeOptions,
      errorText: _errors[fieldName],
      onChanged: (value) {
        setState(() {
          _enhancedFieldValues[fieldName] = value;
          _controllers[fieldName]?.text = value ?? '';
          _errors[fieldName] = null;
        });
      },
    );
  }

  Widget _buildSmartDateField(String fieldName, DocumentDataField field) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final fieldWidth = isMobile ? double.infinity : 400.0; // DBS-style field width

    return SizedBox(
      width: fieldWidth,
      child: SmartDateField(
        field: field,
        initialValue: _controllers[fieldName]?.text,
        errorText: _errors[fieldName],
        onChanged: (value) {
          setState(() {
            _controllers[fieldName]?.text = value ?? '';
            _errors[fieldName] = null;
          });
        },
      ),
    );
  }

  Widget _buildCountryDropdownField(String fieldName, DocumentDataField field) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final fieldWidth = isMobile ? double.infinity : 400.0; // DBS-style field width

    return SizedBox(
      width: fieldWidth,
      child: CountryDropdownField(
        field: field,
        initialValue: _controllers[fieldName]?.text,
        errorText: _errors[fieldName],
        onChanged: (value) {
          setState(() {
            _controllers[fieldName]?.text = value ?? '';
            _errors[fieldName] = null;
        });
      },
      ),
    );
  }

  Widget _buildTraditionalField(String fieldName, DocumentDataField field) {
    switch (field.type) {
      case 'string':  // Most text fields come as 'string' not 'text'
      case 'text':
        return _buildEnhancedTextField(field);
      case 'date':
        return _buildEnhancedDateField(field);
      case 'dropdown':
        return _buildEnhancedDropdownField(field);
      case 'country':
        return _buildEnhancedCountryField(field);
      case 'radio':
      case 'gender':
        return _buildEnhancedRadioField(field);
      case 'title':
        return _buildEnhancedTitleField(field);
      case 'boolean':
        return _buildEnhancedRadioField(field); // Boolean fields as Yes/No radio
      case 'integer':
        return _buildEnhancedTextField(field); // Integer fields as text input
      default:
        return _buildEnhancedTextField(field);
    }
  }

  Widget _buildEnhancedTextField(DocumentDataField field) {
    // Determine max length based on field type
    int? maxLength;
    if (field.name.toLowerCase().contains('first_name') || field.name.toLowerCase().contains('forename')) {
      maxLength = 60;
    } else if (field.name.toLowerCase().contains('last_name') || field.name.toLowerCase().contains('surname')) {
      maxLength = 60;
    } else if (field.name.toLowerCase().contains('contact') || field.name.toLowerCase().contains('phone')) {
      maxLength = 30;
    }

    final isMobile = ResponsiveHelper.isMobile(context);
    final fieldWidth = isMobile ? double.infinity : 400.0; // DBS-style field width

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 8),
        SizedBox(
          width: fieldWidth,
          child: SizedBox(
            height: 48, // DBS field height
            child: TextFormField(
            controller: _controllers[field.name],
            maxLength: maxLength,
            decoration: InputDecoration(
              hintText: 'Enter ${field.label.toLowerCase()}',
              hintStyle: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: DesignConfig.primaryColor,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              counterStyle: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
            validator: field.required
                ? (value) => value?.isEmpty == true ? '${field.label} is required' : null
                : null,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedDateField(DocumentDataField field) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final fieldWidth = isMobile ? double.infinity : 400.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 8),
        SizedBox(
          width: fieldWidth,
          child: SizedBox(
            height: 48,
            child: TextFormField(
            controller: _controllers[field.name],
            decoration: InputDecoration(
              hintText: 'DD/MM/YYYY',
              hintStyle: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: DesignConfig.primaryColor,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              suffixIcon: IconButton(
                icon: Icon(
                  Icons.calendar_today,
                  color: DesignConfig.primaryColor,
                  size: 20,
                ),
              onPressed: () async {
                final currentDate = DateTime.now();
                DateTime? initialDate;

                // Try to parse current value as initial date
                if (_controllers[field.name]?.text.isNotEmpty == true) {
                  try {
                    initialDate = DateFormat('dd/MM/yyyy').parse(_controllers[field.name]!.text);
                  } catch (e) {
                    initialDate = currentDate;
                  }
                } else {
                  initialDate = currentDate;
                }

                final selectedDate = await showDatePicker(
                  context: context,
                  initialDate: initialDate,
                  firstDate: DateTime(1900),
                  lastDate: DateTime(2100),
                  builder: (context, child) {
                    return Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: Theme.of(context).colorScheme.copyWith(
                          primary: DesignConfig.primaryColor,
                        ),
                      ),
                      child: child!,
                    );
                  },
                );

                if (selectedDate != null) {
                  final formattedDate = DateFormat('dd/MM/yyyy').format(selectedDate);
                  _controllers[field.name]?.text = formattedDate;

                  // Trigger validation for birth certificate cross-field validation
                  if (widget.documentType.key.contains('birth_certificate')) {
                    _formKey.currentState?.validate();
                  }
                }
              },
            ),
          ),
          keyboardType: TextInputType.datetime,
          onChanged: (value) {
            // Trigger real-time validation for birth certificate fields
            if (widget.documentType.key.contains('birth_certificate') &&
                (field.name == 'issue_date' || field.name == 'date_of_birth')) {
              setState(() {
                // This will trigger a rebuild and re-validation
              });
              // Force validation of both date fields for birth certificates
              Future.delayed(const Duration(milliseconds: 100), () {
                _formKey.currentState?.validate();
              });
            }
          },
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9/]')),
            LengthLimitingTextInputFormatter(10),
            _DateInputFormatter(),
          ],
          validator: field.required
              ? (value) {
                  if (value?.isEmpty == true) {
                    return '${field.label} is required';
                  }
                  if (value != null && value.isNotEmpty) {
                    try {
                      DateFormat('dd/MM/yyyy').parseStrict(value);

                      // Birth certificate specific validation
                      if (widget.documentType.key.contains('birth_certificate') &&
                          field.name == 'issue_date') {
                        final dateOfBirthController = _controllers['date_of_birth'];
                        if (dateOfBirthController != null) {
                        }
                        if (dateOfBirthController != null && dateOfBirthController.text.isNotEmpty) {
                          final validationError = SmartDocumentService.validateBirthCertificateIssueDate(
                            value,
                            dateOfBirthController.text,
                            widget.documentType.key,
                          );
                          if (validationError != null) {
                            return validationError;
                          }
                        } else {
                        }
                      }

                      // Also validate issue_date when date_of_birth changes
                      if (widget.documentType.key.contains('birth_certificate') &&
                          field.name == 'date_of_birth') {
                        final issueDateController = _controllers['issue_date'];
                        if (issueDateController != null && issueDateController.text.isNotEmpty) {
                          final validationError = SmartDocumentService.validateBirthCertificateIssueDate(
                            issueDateController.text,
                            value,
                            widget.documentType.key,
                          );
                          if (validationError != null) {
                            // Trigger validation on the issue_date field
                            Future.delayed(const Duration(milliseconds: 50), () {
                              _formKey.currentState?.validate();
                            });
                          }
                        }
                      }

                      return null;
                    } catch (e) {
                      return 'Please enter a valid date (DD/MM/YYYY)';
                    }
                  }
                  return null;
                }
              : (value) {
                  if (value != null && value.isNotEmpty) {
                    try {
                      DateFormat('dd/MM/yyyy').parseStrict(value);

                      // Birth certificate specific validation
                      if (widget.documentType.key.contains('birth_certificate') &&
                          field.name == 'issue_date') {
                        final dateOfBirthController = _controllers['date_of_birth'];
                        if (dateOfBirthController != null && dateOfBirthController.text.isNotEmpty) {
                          final validationError = SmartDocumentService.validateBirthCertificateIssueDate(
                            value,
                            dateOfBirthController.text,
                            widget.documentType.key,
                          );
                          if (validationError != null) {
                            return validationError;
                          }
                        }
                      }

                      // Also validate issue_date when date_of_birth changes (optional)
                      if (widget.documentType.key.contains('birth_certificate') &&
                          field.name == 'date_of_birth') {
                        final issueDateController = _controllers['issue_date'];
                        if (issueDateController != null && issueDateController.text.isNotEmpty) {
                          final validationError = SmartDocumentService.validateBirthCertificateIssueDate(
                            issueDateController.text,
                            value,
                            widget.documentType.key,
                          );
                          if (validationError != null) {
                            // Trigger validation on the issue_date field
                            Future.delayed(const Duration(milliseconds: 50), () {
                              _formKey.currentState?.validate();
                            });
                          }
                        }
                      }

                      return null;
                    } catch (e) {
                      return 'Please enter a valid date (DD/MM/YYYY)';
                    }
                  }
                  return null;
                },
            ),
          ),
        ),
      ],
    );
  }





  bool _documentRequiresPhoto() {
    return widget.documentType.requiresPhoto;
  }

  Widget _buildUploadSection() {
    final isMobile = ResponsiveHelper.isMobile(context);
    final fieldWidth = isMobile ? double.infinity : 400.0; // DBS-style field width

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Document Upload',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: fieldWidth,
          child: DocumentUploadWidget(
            documentName: widget.documentType.name,
            onFileSelected: (file) {
              setState(() {
                _uploadedFile = file;
              });
            },
            onUpload: () {
              // Handle upload completion if needed
            },
            allowedExtensions: const ['pdf', 'png', 'jpg', 'jpeg'],
            maxFileSizeInMB: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildAddressConfirmationSection() {
    if (!widget.documentType.confirmsAddress) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Address Confirmation',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Row(
            children: [
              Checkbox(
                value: _confirmsAddress,
                onChanged: (value) {
                  setState(() {
                    _confirmsAddress = value ?? false;
                  });
                },
                activeColor: DesignConfig.primaryColor,
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'This document confirms my current address',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final bool isMobile = ResponsiveHelper.isMobile(context);

    return Row(
      children: [
        SizedBox(
          height: 48.0,
          width: isMobile ? 120 : 160,
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: Colors.grey.shade300, width: 1.5),
              backgroundColor: Colors.white,
              foregroundColor: Colors.grey.shade700,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
              shadowColor: Colors.transparent,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.close,
                  size: 18,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  'Cancel',
                  style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 16),
        SizedBox(
          height: 48.0,
          width: isMobile ? 160 : 200,
          child: ElevatedButton(
            onPressed: _saveDocument,
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignConfig.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
              shadowColor: DesignConfig.primaryColor.withValues(alpha: 0.3),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  widget.existingNomination != null ? Icons.update : Icons.check_circle,
                  size: 18,
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    widget.existingNomination != null ? 'Update Document' : 'Nominate Document',
                    style: const TextStyle(
                      fontSize: 14.0,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _saveDocument() async {

    // Clear previous errors
    setState(() {
      _errors.clear();
    });

    // Manual validation for all fields (since SmartDateField doesn't integrate with FormState)
    bool hasValidationErrors = false;

    for (final field in widget.documentType.dataFields) {
      final value = _controllers[field.name]?.text;

      // Required field validation
      if (field.required && (value == null || value.isEmpty)) {
        setState(() {
          _errors[field.name] = '${field.label} is required';
        });
        hasValidationErrors = true;
        continue;
      }

      // Date format validation for date fields
      if (field.type == 'date' && value != null && value.isNotEmpty) {
        final dateRegex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
        if (!dateRegex.hasMatch(value)) {
          setState(() {
            _errors[field.name] = 'Please enter a valid date (DD/MM/YYYY)';
          });
          hasValidationErrors = true;
          continue;
        }

        // Parse date to check if it's valid
        try {
          final parts = value.split('/');
          final day = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final year = int.parse(parts[2]);
          final date = DateTime(year, month, day);

          // Basic date validation
          if (field.name.contains('birth') && date.isAfter(DateTime.now())) {
            setState(() {
              _errors[field.name] = 'Birth date cannot be in the future';
            });
            hasValidationErrors = true;
            continue;
          }

          if (field.name.contains('issue') && date.isAfter(DateTime.now())) {
            setState(() {
              _errors[field.name] = 'Issue date cannot be in the future';
            });
            hasValidationErrors = true;
            continue;
          }
        } catch (e) {
          setState(() {
            _errors[field.name] = 'Please enter a valid date';
          });
          hasValidationErrors = true;
          continue;
        }
      }
    }

    // Birth certificate specific validation
    if (widget.documentType.key.contains('birth_certificate')) {
      final issueDateValue = _controllers['issue_date']?.text;
      final dateOfBirthValue = _controllers['date_of_birth']?.text;


      // 1. Validate issue date against date of birth (birth certificate timing rules) - only if both dates exist
      if (issueDateValue != null && issueDateValue.isNotEmpty &&
          dateOfBirthValue != null && dateOfBirthValue.isNotEmpty) {

        final issueValidationError = SmartDocumentService.validateBirthCertificateIssueDate(
          issueDateValue,
          dateOfBirthValue,
          widget.documentType.key,
        );


        if (issueValidationError != null) {
          setState(() {
            _errors['issue_date'] = issueValidationError;
          });
          hasValidationErrors = true;
        }
      }

      // 2. Validate document date of birth matches DBS form date of birth - always check if DOB exists
      if (dateOfBirthValue != null && dateOfBirthValue.isNotEmpty &&
          _applicantData != null && _applicantData!.dateOfBirth.isNotEmpty) {

        // Convert document DOB from DD/MM/YYYY to YYYY-MM-DD for comparison
        String documentDOBFormatted;
        try {
          final parts = dateOfBirthValue.split('/');
          documentDOBFormatted = '${parts[2]}-${parts[1].padLeft(2, '0')}-${parts[0].padLeft(2, '0')}';
        } catch (e) {
          setState(() {
            _errors['date_of_birth'] = 'Invalid date format';
          });
          hasValidationErrors = true;
          return;
        }

        final dobMatchError = SmartDocumentService.validateDateOfBirthMatch(
          documentDOBFormatted,
          _applicantData!.dateOfBirth,
        );


        if (dobMatchError != null) {
          setState(() {
            _errors['date_of_birth'] = dobMatchError;
          });
          hasValidationErrors = true;
        }
      } else if (dateOfBirthValue != null && dateOfBirthValue.isNotEmpty) {
      }
    }

    // Also run the traditional form validation for non-SmartDateField fields
    final isFormValid = _formKey.currentState?.validate() ?? false;

    if (hasValidationErrors || !isFormValid) {
      if (hasValidationErrors && _errors.isNotEmpty) {
        final firstError = _errors.values.first;
        _showSnackBar(firstError ?? 'Validation error', isError: true);
      }
      return;
    }


    if (_documentRequiresPhoto() && _uploadedFile == null && widget.existingNomination == null) {
      _showSnackBar('Please upload a document photo', isError: true);
      return;
    }

    final documentData = <String, dynamic>{};

    for (final field in widget.documentType.dataFields) {
      final value = _controllers[field.name]?.text;
      if (value != null && value.isNotEmpty) {
        documentData[field.name] = value.trim();
      }

      if (field.isEnhancedField && _enhancedFieldValues[field.name] != null) {
        documentData['${field.name}_enhanced_response'] = _enhancedFieldValues[field.name];
      }
    }

    final nomination = DocumentNomination(
      documentTypeId: widget.documentType.id,
      documentData: documentData,
      confirmsAddress: _confirmsAddress,
    );

    try {
      final notifier = ref.read(documentNominationProvider.notifier);

      // Store the file in the provider if one was selected
      if (_uploadedFile != null) {
        notifier.storeUploadedFile(nomination.documentTypeId, _uploadedFile!);
      }

      notifier.addDocumentNomination(nomination);

      if (mounted) {
        _showSnackBar(
          widget.existingNomination != null
              ? 'Document updated successfully'
              : 'Document nominated successfully',
          isError: false,
        );
        Navigator.of(context).pop();
      }
    } catch (error) {
      if (mounted) {
        _showSnackBar('Failed to save document: $error', isError: true);
      }
    }
  }


  void _showSnackBar(String message, {required bool isError}) {
    if (!mounted) return;
    CustomSnackBar.show(
      context: context,
      message: message,
      backgroundColor: isError ? AppColors.kRedColor : AppColors.kBlueColor,
      textColor: Colors.white,
      icon: isError ? Icons.error : Icons.check,
      duration: const Duration(seconds: 3),
    );
  }

  Widget _buildFieldLabel(DocumentDataField field) {
    return RichText(
      text: TextSpan(
        text: field.label,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        children: field.required
            ? [
                const TextSpan(
                  text: ' *',
                  style: TextStyle(color: Colors.red),
                ),
              ]
            : null,
      ),
    );
  }

  Widget _buildEnhancedDropdownField(DocumentDataField field) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final fieldWidth = isMobile ? double.infinity : 400.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 8),
        SizedBox(
          width: fieldWidth,
          height: 48,
          child: FormBuilderDropdown<String>(
          name: field.name,
          initialValue: _controllers[field.name]?.text,
          decoration: InputDecoration(
            hintText: 'Select ${field.label.toLowerCase()}',
            hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: DesignConfig.primaryColor, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          ),
          items: (field.options ?? [])
              .map((option) => DropdownMenuItem(
                    value: option,
                    child: Text(
                      option,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ))
              .toList(),
          validator: field.required
              ? FormBuilderValidators.required(errorText: '${field.label} is required')
              : null,
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedCountryField(DocumentDataField field) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final fieldWidth = isMobile ? double.infinity : 400.0;

    final countries = [
      'United Kingdom', 'United States', 'Canada', 'Australia', 'Germany', 'France', 'Spain', 'Italy', 'Netherlands', 'Belgium', 'Switzerland', 'Austria', 'Sweden', 'Norway', 'Denmark', 'Finland', 'Ireland', 'Portugal', 'Greece', 'Poland', 'Czech Republic', 'Hungary', 'Slovakia', 'Slovenia', 'Croatia', 'Romania', 'Bulgaria', 'Lithuania', 'Latvia', 'Estonia', 'Malta', 'Cyprus', 'Luxembourg', 'Iceland', 'Liechtenstein', 'Monaco', 'San Marino', 'Vatican City', 'Andorra', 'India', 'China', 'Japan', 'South Korea', 'Singapore', 'Malaysia', 'Thailand', 'Philippines', 'Indonesia', 'Vietnam', 'Bangladesh', 'Pakistan', 'Sri Lanka', 'Nepal', 'Myanmar', 'Cambodia', 'Laos', 'Brunei', 'Maldives', 'Bhutan', 'Afghanistan', 'Iran', 'Iraq', 'Turkey', 'Saudi Arabia', 'United Arab Emirates', 'Qatar', 'Kuwait', 'Bahrain', 'Oman', 'Yemen', 'Jordan', 'Lebanon', 'Syria', 'Israel', 'Palestine', 'Egypt', 'Libya', 'Tunisia', 'Algeria', 'Morocco', 'Sudan', 'South Sudan', 'Ethiopia', 'Kenya', 'Uganda', 'Tanzania', 'Rwanda', 'Burundi', 'Democratic Republic of Congo', 'Republic of Congo', 'Central African Republic', 'Chad', 'Niger', 'Mali', 'Burkina Faso', 'Ivory Coast', 'Ghana', 'Togo', 'Benin', 'Nigeria', 'Cameroon', 'Equatorial Guinea', 'Gabon', 'Sao Tome and Principe', 'Cape Verde', 'Guinea-Bissau', 'Guinea', 'Sierra Leone', 'Liberia', 'Senegal', 'Gambia', 'Mauritania', 'Western Sahara', 'South Africa', 'Namibia', 'Botswana', 'Zimbabwe', 'Zambia', 'Malawi', 'Mozambique', 'Madagascar', 'Mauritius', 'Seychelles', 'Comoros', 'Djibouti', 'Eritrea', 'Somalia', 'Somaliland', 'Brazil', 'Argentina', 'Chile', 'Peru', 'Colombia', 'Venezuela', 'Ecuador', 'Bolivia', 'Paraguay', 'Uruguay', 'Guyana', 'Suriname', 'French Guiana', 'Mexico', 'Guatemala', 'Belize', 'El Salvador', 'Honduras', 'Nicaragua', 'Costa Rica', 'Panama', 'Cuba', 'Jamaica', 'Haiti', 'Dominican Republic', 'Puerto Rico', 'Trinidad and Tobago', 'Barbados', 'Saint Lucia', 'Saint Vincent and the Grenadines', 'Grenada', 'Antigua and Barbuda', 'Saint Kitts and Nevis', 'Dominica', 'Bahamas', 'Russia', 'Ukraine', 'Belarus', 'Moldova', 'Georgia', 'Armenia', 'Azerbaijan', 'Kazakhstan', 'Uzbekistan', 'Turkmenistan', 'Tajikistan', 'Kyrgyzstan', 'Mongolia', 'North Korea', 'Taiwan', 'Hong Kong', 'Macau', 'New Zealand', 'Fiji', 'Papua New Guinea', 'Solomon Islands', 'Vanuatu', 'New Caledonia', 'French Polynesia', 'Samoa', 'Tonga', 'Kiribati', 'Tuvalu', 'Nauru', 'Palau', 'Marshall Islands', 'Micronesia'
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 8),
        SizedBox(
          width: fieldWidth,
          height: 48, // DBS field height
          child: FormBuilderDropdown<String>(
          name: field.name,
          initialValue: _controllers[field.name]?.text,
          decoration: InputDecoration(
            hintText: 'Select country',
            hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: DesignConfig.primaryColor, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          ),
          items: countries
              .map((country) => DropdownMenuItem(
                    value: country,
                    child: Text(
                      country,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ))
              .toList(),
          validator: field.required
              ? FormBuilderValidators.required(errorText: '${field.label} is required')
              : null,
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedTitleField(DocumentDataField field) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final fieldWidth = isMobile ? double.infinity : 400.0; // DBS-style field width

    final titleOptions = ['Mr', 'Mrs', 'Miss', 'Ms', 'Dr', 'Prof', 'Rev', 'Other'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 8),
        SizedBox(
          width: fieldWidth,
          height: 48, // DBS field height
          child: FormBuilderDropdown<String>(
          name: field.name,
          initialValue: _controllers[field.name]?.text,
          decoration: InputDecoration(
            hintText: 'Select title',
            hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: DesignConfig.primaryColor, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            suffixIcon: const Icon(Icons.arrow_drop_down, color: Colors.grey),
          ),
          items: titleOptions
              .map((title) => DropdownMenuItem(
                    value: title,
                    child: Text(
                      title,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ))
              .toList(),
          validator: field.required
              ? FormBuilderValidators.required(errorText: '${field.label} is required')
              : null,
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedRadioField(DocumentDataField field) {
    List<String> options = [];
    if (field.type == 'gender' || field.name.toLowerCase().contains('gender')) {
      options = ['Male', 'Female'];
    } else if (field.options != null && field.options!.isNotEmpty) {
      options = field.options!;
    } else {
      options = ['Yes', 'No'];
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 12),
        FormBuilderField<String>(
          name: field.name,
          initialValue: _controllers[field.name]?.text,
          validator: field.required
              ? (value) => value == null ? '${field.label} is required' : null
              : null,
          builder: (FormFieldState<String> fieldState) {
            return Column(
              children: [
                Row(
                  children: options.map((option) {
                    return Expanded(
                      child: GestureDetector(
                        onTap: () => fieldState.didChange(option),
                        child: Container(
                          margin: EdgeInsets.only(
                            right: option != options.last ? 12 : 0,
                          ),
                          child: Row(
                            children: [
                              Radio<String>(
                                value: option,
                                groupValue: fieldState.value,
                                onChanged: (String? value) {
                                  if (value != null) {
                                    fieldState.didChange(value);
                                  }
                                },
                                activeColor: DesignConfig.primaryColor,
                                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              Expanded(
                                child: Text(
                                  option,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.black87,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
                if (fieldState.hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      fieldState.errorText!,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ],
    );
  }
}

class _DateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    String digitsOnly = text.replaceAll(RegExp(r'[^0-9/]'), '');

    String formatted = '';
    int digitCount = 0;

    for (int i = 0; i < digitsOnly.length; i++) {
      final char = digitsOnly[i];

      if (char == '/') {
        if (digitCount == 2 || digitCount == 5) {
          formatted += char;
        }
        continue;
      }

      formatted += char;
      digitCount++;

      if (digitCount == 2 && i < digitsOnly.length - 1 && digitsOnly[i + 1] != '/') {
        formatted += '/';
      } else if (digitCount == 4 && i < digitsOnly.length - 1 && digitsOnly[i + 1] != '/') {
        formatted += '/';
      }

      if (digitCount >= 8) break;
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
