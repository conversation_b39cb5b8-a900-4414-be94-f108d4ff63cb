import 'dart:io';

import 'package:SolidCheck/core/config/design_config.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:SolidCheck/features/document_nomination/data/services/document_upload_api_service.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';

enum UploadState {
  initial,
  uploading,
  success,
  error,
}

enum UploadError {
  wrongFormat,
  fileTooLarge,
  networkError,
}

class DocumentUploadWidget extends StatefulWidget {
  final String documentName;
  final Function(File file, String fileName) onFileSelected;
  final VoidCallback? onCancel;
  final VoidCallback? onUpload;
  final List<String> allowedExtensions;
  final int maxFileSizeInMB;
  final String? applicationId;
  final String? nominationId;

  const DocumentUploadWidget({
    super.key,
    required this.documentName,
    required this.onFileSelected,
    this.onCancel,
    this.onUpload,
    this.allowedExtensions = const ['pdf', 'png', 'jpg', 'jpeg'],
    this.maxFileSizeInMB = 10,
    this.applicationId,
    this.nominationId,
  });

  @override
  State<DocumentUploadWidget> createState() => _DocumentUploadWidgetState();
}

class _DocumentUploadWidgetState extends State<DocumentUploadWidget> {
  UploadState _uploadState = UploadState.initial;
  UploadError? _uploadError;
  File? _selectedFile;
  String? _fileName;
  String? _fileExtension;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DesignConfig.cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getBorderColor(),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          if (_selectedFile == null) _buildUploadArea() else _buildFilePreview(),
          const SizedBox(height: 16),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildUploadArea() {
    return Column(
      children: [
        InkWell(
          onTap: _uploadState == UploadState.uploading ? null : _pickFile,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 32),
            decoration: BoxDecoration(
              color: _getUploadAreaColor(),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _getUploadAreaBorderColor(),
                style: BorderStyle.solid,
                width: 2,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.cloud_upload_outlined,
                  size: 48,
                  color: _getIconColor(),
                ),
                const SizedBox(height: 12),
                Text(
                  _getUploadText(),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: _getTextColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 12),
                ElevatedButton.icon(
                  onPressed: _uploadState == UploadState.uploading ? null : _pickFile,
                  icon: const Icon(Icons.folder_open),
                  label: const Text(kIsWeb ? 'Choose File (Web)' : 'Choose File'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: DesignConfig.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Platform: ${kIsWeb ? 'Web' : 'Mobile/Desktop'}',
                  style: TextStyle(fontSize: 10, color: Colors.grey),
                ),
                if (_uploadError != null) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _getErrorMessage(),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFilePreview() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(
            _getFileIcon(),
            size: 32,
            color: DesignConfig.primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _fileName ?? 'Unknown file',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: DesignConfig.primaryTextColor,
                  ),
                ),
                Text(
                  _fileExtension?.toUpperCase() ?? '',
                  style: TextStyle(
                    fontSize: 12,
                    color: DesignConfig.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          if (_uploadState == UploadState.uploading)
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          else if (_uploadState == UploadState.success)
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 20,
            ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        if (_selectedFile != null) ...[
          Expanded(
            child: OutlinedButton(
              onPressed: _uploadState == UploadState.uploading ? null : _clearFile,
              style: OutlinedButton.styleFrom(
                foregroundColor: DesignConfig.secondaryTextColor,
                side: BorderSide(color: DesignConfig.primaryBorderColor),
              ),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 12),
        ],
        Expanded(
          child: ElevatedButton(
            onPressed: _getUploadButtonAction(),
            style: ElevatedButton.styleFrom(
              backgroundColor: _getUploadButtonColor(),
              foregroundColor: Colors.white,
            ),
            child: Text(_getUploadButtonText()),
          ),
        ),
      ],
    );
  }

  Future<void> _pickFile() async {
    try {
      setState(() {
        _uploadState = UploadState.uploading;
        _uploadError = null;
      });

      FilePickerResult? result;

      // Try different approaches based on platform
      if (kIsWeb) {
        // For web, try the simplest approach first
        try {
          result = await FilePicker.platform.pickFiles(
            allowMultiple: false,
            withData: true,
          );
        } catch (e) {
          // If that fails, try with type specification
          result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: widget.allowedExtensions,
            allowMultiple: false,
            withData: true,
          );
        }
      } else {
        // For mobile/desktop, use custom extensions
        result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: widget.allowedExtensions,
          allowMultiple: false,
        );
      }

      if (result != null && result.files.isNotEmpty) {
        final platformFile = result.files.first;

        // Validate file extension manually for web
        if (kIsWeb) {
          final fileName = platformFile.name.toLowerCase();
          final hasValidExtension = widget.allowedExtensions.any(
            (ext) => fileName.endsWith('.$ext'),
          );

          if (!hasValidExtension) {
            setState(() {
              _uploadError = UploadError.wrongFormat;
              _uploadState = UploadState.error;
            });
            return;
          }
        }

        final fileSizeInMB = platformFile.size / (1024 * 1024);

        if (fileSizeInMB > widget.maxFileSizeInMB) {
          setState(() {
            _uploadError = UploadError.fileTooLarge;
            _uploadState = UploadState.error;
          });
          return;
        }

        // Create File object (for non-web platforms)
        File? file;
        if (!kIsWeb && platformFile.path != null) {
          file = File(platformFile.path!);
        } else {
          // For web, create a dummy file with the name
          file = File('web_${platformFile.name}');
        }

        setState(() {
          _selectedFile = file;
          _fileName = platformFile.name;
          _fileExtension = platformFile.extension;
          _uploadError = null;
          _uploadState = UploadState.success;
        });

        // Call the callback
        widget.onFileSelected(file, platformFile.name);
      } else {
        setState(() {
          _uploadState = UploadState.initial;
        });
      }
    } catch (e) {
      setState(() {
        _uploadError = UploadError.networkError;
        _uploadState = UploadState.error;
      });
    }
  }

  void _clearFile() {
    setState(() {
      _selectedFile = null;
      _fileName = null;
      _fileExtension = null;
      _uploadError = null;
      _uploadState = UploadState.initial;
    });
  }

  Future<void> _uploadFile() async {
    if (_selectedFile == null) return;

    print('🚀 Starting file upload: ${_selectedFile!.path}');

    setState(() {
      _uploadState = UploadState.uploading;
    });

    try {
      final applicationId = widget.applicationId ?? '';
      final nominationId = widget.nominationId ?? '';

      print('📋 Upload details: applicationId=$applicationId, nominationId=$nominationId');

      if (applicationId.isEmpty || nominationId.isEmpty) {
        print('❌ Missing IDs - using mock upload for now');
        // For now, simulate upload if IDs are missing
        await Future.delayed(const Duration(seconds: 2));

        if (mounted) {
          setState(() {
            _uploadState = UploadState.success;
          });
          widget.onUpload?.call();
        }
        return;
      }

      // TODO: Implement actual file upload when authentication is available
      // For now, simulate the upload
      print('🌐 Simulating file upload to backend...');
      await Future.delayed(const Duration(seconds: 2));

      print('✅ File upload simulated successfully');

      if (mounted) {
        setState(() {
          _uploadState = UploadState.success;
        });

        widget.onUpload?.call();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('File selected successfully: ${_selectedFile!.path.split('/').last}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print('❌ File upload failed: $e');

      if (mounted) {
        setState(() {
          _uploadError = UploadError.networkError;
          _uploadState = UploadState.error;
        });

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Upload failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Color _getBorderColor() {
    switch (_uploadState) {
      case UploadState.error:
        return Colors.red;
      case UploadState.success:
        return Colors.green;
      default:
        return DesignConfig.primaryBorderColor;
    }
  }

  Color _getUploadAreaColor() {
    switch (_uploadState) {
      case UploadState.error:
        return Colors.red.withValues(alpha: 0.05);
      default:
        return Colors.grey[50]!;
    }
  }

  Color _getUploadAreaBorderColor() {
    switch (_uploadState) {
      case UploadState.error:
        return Colors.red.withValues(alpha: 0.3);
      default:
        return DesignConfig.primaryBorderColor;
    }
  }

  Color _getIconColor() {
    switch (_uploadState) {
      case UploadState.error:
        return Colors.red;
      default:
        return DesignConfig.primaryColor;
    }
  }

  Color _getTextColor() {
    switch (_uploadState) {
      case UploadState.error:
        return Colors.red;
      default:
        return DesignConfig.primaryTextColor;
    }
  }

  String _getUploadText() {
    switch (_uploadState) {
      case UploadState.uploading:
        return 'Uploading...';
      case UploadState.error:
        return 'Select your file or drag and drop\nonly ${widget.allowedExtensions.join(', ').toUpperCase()} file accepted';
      default:
        return 'Select your file or drag and drop\nonly ${widget.allowedExtensions.join(', ').toUpperCase()} file accepted';
    }
  }

  String _getErrorMessage() {
    switch (_uploadError) {
      case UploadError.wrongFormat:
        return 'File format not accepted. Only ${widget.allowedExtensions.join(', ').toUpperCase()} files allowed.';
      case UploadError.fileTooLarge:
        return 'File too large. Maximum size is ${widget.maxFileSizeInMB}MB.';
      case UploadError.networkError:
        return 'Upload failed. Please try again.';
      default:
        return '';
    }
  }

  IconData _getFileIcon() {
    switch (_fileExtension?.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'png':
      case 'jpg':
      case 'jpeg':
        return Icons.image;
      default:
        return Icons.description;
    }
  }

  VoidCallback? _getUploadButtonAction() {
    if (_uploadState == UploadState.uploading) return null;
    if (_selectedFile == null) return _pickFile;
    if (_uploadState == UploadState.success) return null;
    return _uploadFile;
  }

  Color _getUploadButtonColor() {
    switch (_uploadState) {
      case UploadState.success:
        return Colors.green;
      case UploadState.uploading:
        return Colors.grey;
      default:
        return DesignConfig.primaryColor;
    }
  }

  String _getUploadButtonText() {
    if (_selectedFile == null) return 'Browse';
    switch (_uploadState) {
      case UploadState.uploading:
        return 'Uploading...';
      case UploadState.success:
        return 'Uploaded';
      default:
        return 'Upload';
    }
  }
}
