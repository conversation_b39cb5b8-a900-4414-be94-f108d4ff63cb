import 'dart:io';

import 'package:SolidCheck/core/config/component_config.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_fields/dbs_form_builder_fields.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/document_upload_widget.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/enhanced_form_fields.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_document_service.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_name_validation_service.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_postcode_service.dart';
import 'package:SolidCheck/shared/data/country_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:intl/intl.dart';

class DocumentDataForm extends StatefulWidget {
  final DocumentType documentType;
  final DocumentNomination? existingNomination;
  final Function(DocumentNomination) onSave;
  final VoidCallback onCancel;

  const DocumentDataForm({
    super.key,
    required this.documentType,
    this.existingNomination,
    required this.onSave,
    required this.onCancel,
  });

  @override
  State<DocumentDataForm> createState() => _DocumentDataFormState();
}

class _DocumentDataFormState extends State<DocumentDataForm> {
  final _formKey = GlobalKey<FormBuilderState>();
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, String?> _errors = {};
  final Map<String, dynamic> _enhancedFieldValues = {};
  bool _confirmsAddress = false;
  File? _uploadedFile;
  bool _hasUploadedFile = false;
  ApplicantDetailsData? _applicantData;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _confirmsAddress = widget.existingNomination?.confirmsAddress ?? false;
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _initializeControllers() {
    for (final field in widget.documentType.dataFields) {
      final fieldName = field.name;
      final existingValue = widget.existingNomination?.documentData[fieldName]?.toString() ?? '';
      _controllers[fieldName] = TextEditingController(text: existingValue);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: ResponsiveHelper.getResponsiveValue(
          context,
          mobile: MediaQuery.of(context).size.width * 0.9,
          tablet: 500.0,
          desktop: 600.0,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.9,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              Flexible(
                child: SingleChildScrollView(
                  child: _buildForm(),
                ),
              ),
              _buildActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.kBlueColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.description,
            color: AppColors.kBlueColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Document Details',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 18.0,
                      tablet: 20.0,
                      desktop: 20.0,
                    ),
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.documentType.name,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 14.0,
                      tablet: 15.0,
                      desktop: 15.0,
                    ),
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: widget.onCancel,
            icon: const Icon(Icons.close, color: Colors.grey),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: FormBuilder(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...widget.documentType.dataFields.map((field) {
              return _buildFormField(field.name, field);
            }),
            const SizedBox(height: 16),
            if (_documentRequiresPhoto())
              _buildUploadSection(),
            if (_documentRequiresPhoto())
              const SizedBox(height: 16),
            _buildAddressConfirmationCheckbox(),
          ],
        ),
      ),
    );
  }

  Widget _buildFormField(String fieldName, DocumentDataField field) {
    final enhancedType = field.enhancedType;

    // Use enhanced field components for better UX
    switch (enhancedType) {
      case 'yes_no_confirmation':
        return _buildYesNoConfirmationField(fieldName, field);
      case 'multiple_choice_postcode':
        return _buildMultipleChoicePostcodeField(fieldName, field);
      case 'smart_date':
        return _buildSmartDateField(fieldName, field);
      case 'country_dropdown':
        return _buildCountryDropdownField(fieldName, field);
      default:
        return _buildTraditionalField(fieldName, field);
    }
  }

  Widget _buildYesNoConfirmationField(String fieldName, DocumentDataField field) {
    final confirmationText = SmartNameValidationService.generateNameConfirmationText(
      fieldName,
      _applicantData,
    );

    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            confirmationText,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              SizedBox(
                width: 80,
                child: _buildYesNoOption(
                  'Yes',
                  'yes',
                  fieldName,
                  Icons.check_circle,
                  Colors.green,
                  _enhancedFieldValues[fieldName] == 'yes',
                ),
              ),
              const SizedBox(width: 12),
              SizedBox(
                width: 80,
                child: _buildYesNoOption(
                  'No',
                  'no',
                  fieldName,
                  Icons.cancel,
                  Colors.red,
                  _enhancedFieldValues[fieldName] == 'no',
                ),
              ),
            ],
          ),
          if (_errors[fieldName] != null) ...[
            const SizedBox(height: 8),
            Text(
              _errors[fieldName]!,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildYesNoOption(String label, String value, String fieldName, IconData icon, Color color, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _enhancedFieldValues[fieldName] = value;
          _errors[fieldName] = null;

          // Set the expected value in the controller for form submission
          if (value == 'yes') {
            final expectedValue = SmartNameValidationService.getExpectedNameValue(
              fieldName,
              _applicantData,
            );
            _controllers[fieldName]?.text = expectedValue;
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(6),
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.white,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey,
              size: 16,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : Colors.grey.shade700,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMultipleChoicePostcodeField(String fieldName, DocumentDataField field) {
    final postcodeOptions = SmartPostcodeService.generatePostcodeOptions(
      _applicantData?.currentAddress.postcode ?? 'LE7 7AQ',
      _applicantData,
    );

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: MultipleChoicePostcodeField(
        field: field,
        initialValue: _enhancedFieldValues[fieldName],
        postcodeOptions: postcodeOptions,
        errorText: _errors[fieldName],
        onChanged: (value) {
          setState(() {
            _enhancedFieldValues[fieldName] = value;
            _controllers[fieldName]?.text = value ?? '';
            _errors[fieldName] = null;
          });
        },
      ),
    );
  }

  Widget _buildSmartDateField(String fieldName, DocumentDataField field) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: SmartDateField(
        field: field,
        initialValue: _controllers[fieldName]?.text,
        errorText: _errors[fieldName],
        onChanged: (value) {
          setState(() {
            _controllers[fieldName]?.text = value ?? '';
            _errors[fieldName] = null;
          });
        },
      ),
    );
  }

  Widget _buildCountryDropdownField(String fieldName, DocumentDataField field) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: CountryDropdownField(
        field: field,
        initialValue: _controllers[fieldName]?.text,
        errorText: _errors[fieldName],
        onChanged: (value) {
          setState(() {
            _controllers[fieldName]?.text = value ?? '';
            _errors[fieldName] = null;
          });
        },
      ),
    );
  }

  Widget _buildTraditionalField(String fieldName, DocumentDataField field) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: _buildFormBuilderField(fieldName, field),
    );
  }

  Widget _buildFormBuilderField(String fieldName, DocumentDataField field) {
    final initialValue = widget.existingNomination?.documentData[fieldName]?.toString();

    if (field.type == 'date') {
      return _buildDBSDateField(fieldName, field, initialValue);
    } else if (field.options != null) {
      return _buildDBSDropdownField(fieldName, field, initialValue);
    } else if (fieldName.toLowerCase().contains('country')) {
      return _buildDBSCountryDropdownField(fieldName, field, initialValue);
    } else {
      return _buildDBSTextField(fieldName, field, initialValue);
    }
  }

  Widget _buildDBSTextField(String fieldName, DocumentDataField field, String? initialValue) {
    return DBSFormBuilderTextField(
      name: fieldName,
      label: _formatFieldLabel(fieldName, field.required),
      isRequired: field.required,
      hint: field.placeholder ?? _getDefaultPlaceholder(fieldName),
      initialValue: initialValue,
      validators: [
        if (field.required)
          FormBuilderValidators.required(errorText: '${field.label} is required'),
      ],
      onChanged: (value) {
        setState(() {
          _errors[fieldName] = null;
        });
      },
    );
  }

  Widget _buildDBSDateField(String fieldName, DocumentDataField field, String? initialValue) {
    DateTime? parsedInitialValue;
    if (initialValue != null && initialValue.isNotEmpty) {
      try {
        parsedInitialValue = DateFormat('dd/MM/yyyy').parse(initialValue);
      } catch (e) {
        // If parsing fails, leave as null
      }
    }

    return DBSFormBuilderDatePicker(
      name: fieldName,
      label: _formatFieldLabel(fieldName, field.required),
      isRequired: field.required,
      initialValue: parsedInitialValue,
      firstDate: DateTime(1900),
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
      validators: [
        if (field.required)
          FormBuilderValidators.required(errorText: '${field.label} is required'),
      ],
      onChanged: (value) {
        setState(() {
          _errors[fieldName] = null;

          // For birth certificates, when date_of_birth changes, re-validate issue_date
          if (widget.documentType.key.contains('birth_certificate') &&
              fieldName == 'date_of_birth') {
            _errors['issue_date'] = null;
            // Trigger validation of issue_date field if it has a value
            final issueDateField = _formKey.currentState?.fields['issue_date'];
            final issueDateValue = issueDateField?.value;
            if (issueDateValue != null) {
              final issueField = widget.documentType.enhancedDataFields
                  .firstWhere((f) => f.name == 'issue_date');
              final issueDateString = DateFormat('dd/MM/yyyy').format(issueDateValue);
              final validationError = _validateField('issue_date', issueField, issueDateString);
              if (validationError != null) {
                _errors['issue_date'] = validationError;
              }
            }
          }

          // For birth certificates, when issue_date changes, validate against date_of_birth
          if (widget.documentType.key.contains('birth_certificate') &&
              fieldName == 'issue_date') {
            final dateOfBirthController = _controllers['date_of_birth'];
            if (dateOfBirthController != null && dateOfBirthController.text.isNotEmpty) {
              final issueField = widget.documentType.enhancedDataFields
                  .firstWhere((f) => f.name == 'issue_date');
              final issueDateString = value != null ? DateFormat('dd/MM/yyyy').format(value) : null;
              final validationError = _validateField('issue_date', issueField, issueDateString);
              if (validationError != null) {
                _errors['issue_date'] = validationError;
              }
            }
          }
        });
      },
    );
  }

  Widget _buildDBSDropdownField(String fieldName, DocumentDataField field, String? initialValue) {
    final dropdownItems = field.options?.map((option) =>
      DropdownMenuItem<String>(
        value: option,
        child: Text(option),
      )
    ).toList() ?? [];

    return DBSFormBuilderDropdown<String>(
      name: fieldName,
      label: _formatFieldLabel(fieldName, field.required),
      isRequired: field.required,
      hint: 'Select ${field.label.toLowerCase()}',
      items: dropdownItems,
      initialValue: initialValue,
      validators: [
        if (field.required)
          FormBuilderValidators.required(errorText: '${field.label} is required'),
      ],
      onChanged: (value) {
        setState(() {
          _errors[fieldName] = null;
        });
      },
    );
  }

  Widget _buildDBSCountryDropdownField(String fieldName, DocumentDataField field, String? initialValue) {
    return DBSFormBuilderDropdown<String>(
      name: fieldName,
      label: _formatFieldLabel(fieldName, field.required),
      isRequired: field.required,
      hint: 'Select country',
      items: Countries.getCountryDropdownItems(),
      initialValue: initialValue,
      validators: [
        if (field.required)
          FormBuilderValidators.required(errorText: '${field.label} is required'),
      ],
      onChanged: (value) {
        setState(() {
          _errors[fieldName] = null;
        });
      },
    );
  }

  Widget _buildAddressConfirmationCheckbox() {
    return CheckboxListTile(
      title: Text(
        'This document confirms my current address',
        style: TextStyle(
          fontSize: ResponsiveHelper.getResponsiveFontSize(
            context,
            mobile: 14.0,
            tablet: 15.0,
            desktop: 15.0,
          ),
        ),
      ),
      value: _confirmsAddress,
      onChanged: (value) {
        setState(() {
          _confirmsAddress = value ?? false;
        });
      },
      controlAffinity: ListTileControlAffinity.leading,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildActions() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: widget.onCancel,
              style: ComponentConfig.secondaryButtonStyle,
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _handleSave,
              style: ComponentConfig.primaryButtonStyle,
              child: const Text('Add Document'),
            ),
          ),
        ],
      ),
    );
  }

  void _handleSave() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      // Validate file upload for documents requiring photos
      if (_documentRequiresPhoto() && !_hasUploadedFile) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please upload a document file before proceeding.'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final formData = _formKey.currentState!.value;
      final documentData = <String, dynamic>{};

      // Get form data from FormBuilder
      for (final field in widget.documentType.dataFields) {
        final value = formData[field.name];
        if (value != null) {
          if (value is DateTime) {
            // Format dates as YYYY-MM-DD for backend compatibility
            documentData[field.name] = DateFormat('yyyy-MM-dd').format(value);
          } else {
            documentData[field.name] = value.toString();
          }
        }
      }

      // Add enhanced field metadata for future reference
      for (final field in widget.documentType.dataFields) {
        if (field.isEnhancedField && _enhancedFieldValues[field.name] != null) {
          documentData['${field.name}_enhanced_response'] = _enhancedFieldValues[field.name];
        }
      }

      // Add file information if uploaded
      if (_uploadedFile != null) {
        documentData['uploaded_file_path'] = _uploadedFile!.path;
        documentData['uploaded_file_name'] = _uploadedFile!.path.split('/').last;
      }

      try {
        print('Creating nomination for document: ${widget.documentType.key}');
        print('Document type ID: ${widget.documentType.id} (${widget.documentType.id.runtimeType})');
        print('Confirms address: $_confirmsAddress (${_confirmsAddress.runtimeType})');
        print('Document data: $documentData');

        final nomination = DocumentNomination(
          documentTypeId: widget.documentType.id,
          documentData: documentData,
          confirmsAddress: _confirmsAddress,
        );

        print('Created nomination successfully: $nomination');
        widget.onSave(nomination);
      } catch (e, stackTrace) {
        print('Error creating nomination: $e');
        print('Stack trace: $stackTrace');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating nomination: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String? _validateField(String fieldName, DocumentDataField field, String? value) {
    final enhancedType = field.enhancedType;

    // Handle enhanced field validation
    if (enhancedType == 'yes_no_confirmation') {
      return SmartNameValidationService.validateNameConfirmation(
        _enhancedFieldValues[fieldName],
        fieldName,
        _applicantData,
      );
    }

    if (enhancedType == 'multiple_choice_postcode') {
      if (field.required && _enhancedFieldValues[fieldName] == null) {
        return 'Please select a postcode';
      }
      return null;
    }

    // Traditional field validation
    if (field.required && (value == null || value.isEmpty)) {
      return 'This field is required';
    }

    if (field.type == 'date' && value != null && value.isNotEmpty) {
      final dateRegex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
      if (!dateRegex.hasMatch(value)) {
        return 'Please enter a valid date (DD/MM/YYYY)';
      }
    }

    // Birth certificate specific validation
    if (widget.documentType.key.contains('birth_certificate') &&
        fieldName == 'issue_date' &&
        value != null &&
        value.isNotEmpty) {

      // Get date of birth value from form field
      final dateOfBirthField = _formKey.currentState?.fields['date_of_birth'];
      final dateOfBirthValue = dateOfBirthField?.value;

      if (dateOfBirthValue != null) {
        final dateOfBirthString = DateFormat('dd/MM/yyyy').format(dateOfBirthValue);
        final validationError = SmartDocumentService.validateBirthCertificateIssueDate(
          value,
          dateOfBirthString,
          widget.documentType.key,
        );
        if (validationError != null) {
          return validationError;
        }
      }
    }

    return null;
  }

  String _formatFieldLabel(String fieldName, bool required) {
    final formatted = fieldName
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
    
    return required ? '$formatted *' : formatted;
  }

  String _getDefaultPlaceholder(String fieldName) {
    switch (fieldName.toLowerCase()) {
      case 'passport_number':
        return 'e.g., *********';
      case 'issue_country':
        return 'e.g., United Kingdom';
      case 'licence_number':
        return 'e.g., ABCD*********EF';
      case 'sort_code':
        return 'e.g., 12-34-56';
      case 'account_number':
        return 'e.g., ********';
      default:
        return 'Enter ${_formatFieldLabel(fieldName, false).toLowerCase()}';
    }
  }

  bool _documentRequiresPhoto() {
    // Check API flag first
    if (widget.documentType.requiresPhoto) {
      return true;
    }

    // Fallback to name-based detection
    final photoRequiredDocuments = [
      'passport',
      'driving licence',
      'driving license',
      'photo card',
      'identity card',
      'id card',
      'adoption certificate',
      'birth certificate',
      'hm forces id',
      'fire arms license',
      'firearms license',
    ];

    return photoRequiredDocuments.any((photoDoc) =>
        widget.documentType.name.toLowerCase().contains(photoDoc.toLowerCase()));
  }

  Widget _buildUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Document File (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'You can optionally attach a photo or scan of your document',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 12),
        DocumentUploadWidget(
          documentName: widget.documentType.name,
          onFileSelected: (File file, String fileName) {
            setState(() {
              _uploadedFile = file;
              _hasUploadedFile = true;
            });
          },
          onUpload: () {
            // Handle upload completion if needed
          },
          allowedExtensions: const ['pdf', 'png', 'jpg', 'jpeg'],
          maxFileSizeInMB: 10,
        ),
      ],
    );
  }
}
