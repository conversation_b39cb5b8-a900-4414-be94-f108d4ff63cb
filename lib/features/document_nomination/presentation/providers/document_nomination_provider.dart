import 'dart:io';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/auth/domain/repositories/auth_repository.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart' as auth_wrapper;
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/dbs/data/services/dbs_api_service.dart';
import 'package:SolidCheck/features/document_nomination/data/models/available_documents_response.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/data/models/validation_response.dart';
import 'package:SolidCheck/features/document_nomination/data/repositories/document_nomination_repository.dart';
import 'package:SolidCheck/features/document_nomination/data/services/document_upload_api_service.dart';
import 'package:SolidCheck/services/entity_options_service.dart';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final entityOptionsServiceProvider = Provider<EntityOptionsService>((ref) {
  return EntityOptionsService();
});

final documentNominationProvider = StateNotifierProvider<DocumentNominationNotifier, DocumentNominationState>((ref) {
  final repository = ref.read(documentNominationRepositoryProvider);
  final entityOptionsService = ref.read(entityOptionsServiceProvider);
  final authRepository = ref.read(auth_wrapper.authProvider);
  return DocumentNominationNotifier(repository, entityOptionsService, authRepository);
});

class DocumentNominationState {
  final bool isLoading;
  final String? error;
  final AvailableDocumentsData? availableDocuments;
  final int selectedRoute;
  final List<DocumentNomination> nominations;
  final ValidationData? lastValidation;
  final bool isValidating;
  final bool isSubmitting;
  final String? successMessage;
  final Map<String, bool> entityOptions;
  final ApplicantDetailsData? applicantData;
  final Map<int, File> uploadedFiles; // documentTypeId -> File
  final Map<int, String> uploadedFileNames; // documentTypeId -> fileName

  DocumentNominationState({
    this.isLoading = false,
    this.error,
    this.availableDocuments,
    this.selectedRoute = 1,
    this.nominations = const [],
    this.lastValidation,
    this.isValidating = false,
    this.isSubmitting = false,
    this.successMessage,
    this.entityOptions = const {},
    this.applicantData,
    this.uploadedFiles = const {},
    this.uploadedFileNames = const {},
  });

  DocumentNominationState copyWith({
    bool? isLoading,
    String? error,
    AvailableDocumentsData? availableDocuments,
    int? selectedRoute,
    List<DocumentNomination>? nominations,
    ValidationData? lastValidation,
    bool? isValidating,
    bool? isSubmitting,
    String? successMessage,
    Map<String, bool>? entityOptions,
    ApplicantDetailsData? applicantData,
    Map<int, File>? uploadedFiles,
    Map<int, String>? uploadedFileNames,
  }) {
    return DocumentNominationState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      availableDocuments: availableDocuments ?? this.availableDocuments,
      selectedRoute: selectedRoute ?? this.selectedRoute,
      nominations: nominations ?? this.nominations,
      lastValidation: lastValidation ?? this.lastValidation,
      isValidating: isValidating ?? this.isValidating,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      successMessage: successMessage,
      entityOptions: entityOptions ?? this.entityOptions,
      applicantData: applicantData ?? this.applicantData,
      uploadedFiles: uploadedFiles ?? this.uploadedFiles,
      uploadedFileNames: uploadedFileNames ?? this.uploadedFileNames,
    );
  }

  bool get canProceed => lastValidation?.validationResult.canProceed ?? false;
  bool get routeCompleted => lastValidation?.validationResult.routeCompleted ?? false;
  bool get hasNominations => nominations.isNotEmpty;
  bool get isAiDocumentScannerEnabled => entityOptions['allow_aidoc_scanner'] ?? false;
  
  List<DocumentType> get availableDocumentsForRoute {
    if (availableDocuments == null) return [];
    
    final allDocs = <DocumentType>[];
    for (final docList in availableDocuments!.availableDocuments.values) {
      allDocs.addAll(docList);
    }
    return allDocs;
  }

  Map<String, List<DocumentType>> get groupedDocuments {
    return availableDocuments?.availableDocuments ?? {};
  }

  bool isDocumentNominated(int documentTypeId) {
    return nominations.any((nom) => nom.documentTypeId == documentTypeId);
  }

  DocumentNomination? getNominationForDocument(int documentTypeId) {
    try {
      return nominations.firstWhere((nom) => nom.documentTypeId == documentTypeId);
    } catch (e) {
      return null;
    }
  }
}

class DocumentNominationNotifier extends StateNotifier<DocumentNominationState> {
  final DocumentNominationRepository _repository;
  final EntityOptionsService _entityOptionsService;
  final auth_wrapper.AuthRepository? _authRepository;

  DocumentNominationNotifier(this._repository, this._entityOptionsService, this._authRepository) : super(DocumentNominationState());

  Future<void> loadAvailableDocuments(String applicationId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _repository.getAvailableDocuments(applicationId);

      if (response.success) {
        // Load applicant data FIRST before showing UI
        ApplicantDetailsData? applicantData;
        try {
          applicantData = await _loadApplicantData(applicationId);
        } catch (e) {
        }

        // Load entity options
        Map<String, bool> entityOptions = {
          'allow_aidoc_scanner': false,
          'require_document_verification': true,
          'allow_manual_document_entry': true,
          'enable_document_upload': true,
        };
        try {
          await _loadEntityOptionsFromCurrentUser();
          entityOptions = state.entityOptions; // Use loaded options
        } catch (e) {
        }

        // Set complete state with all data loaded
        try {
          print('Setting state with ${response.data.currentNominations.length} current nominations');
          for (int i = 0; i < response.data.currentNominations.length; i++) {
            final nom = response.data.currentNominations[i];
            print('Current nomination $i: documentTypeId=${nom.documentTypeId} (${nom.documentTypeId.runtimeType}), confirmsAddress=${nom.confirmsAddress} (${nom.confirmsAddress.runtimeType})');
          }

          // Convert date formats in existing nominations to match backend expectations
          final convertedNominations = response.data.currentNominations.map((nomination) {
            final convertedData = Map<String, dynamic>.from(nomination.documentData);

            // Convert date fields from DD/MM/YYYY to YYYY-MM-DD
            for (final key in convertedData.keys) {
              final value = convertedData[key];
              if (value is String && _isDateField(key) && _isOldDateFormat(value)) {
                try {
                  // Parse DD/MM/YYYY format
                  final parts = value.split('/');
                  if (parts.length == 3) {
                    final day = parts[0].padLeft(2, '0');
                    final month = parts[1].padLeft(2, '0');
                    final year = parts[2];
                    convertedData[key] = '$year-$month-$day';
                    print('Converted date $key: $value → ${convertedData[key]}');
                  }
                } catch (e) {
                  print('Error converting date $key: $value - $e');
                }
              }
            }

            return nomination.copyWith(documentData: convertedData);
          }).toList();

          state = state.copyWith(
            isLoading: false,
            availableDocuments: response.data,
            selectedRoute: response.data.recommendedRoute,
            nominations: convertedNominations,
            entityOptions: entityOptions,
            applicantData: applicantData,
          );

          print('State set successfully with ${state.nominations.length} nominations');
        } catch (e, stackTrace) {
          print('Error setting state: $e');
          print('Stack trace: $stackTrace');
          state = state.copyWith(
            isLoading: false,
            error: 'Error loading nominations: ${e.toString()}',
          );
          return;
        }

        // Completely disable automatic validation to prevent type errors
        // The validation can be triggered manually later
        // if (state.hasNominations) {
        //   try {
        //     await _validateCurrentNominations(applicationId);
        //   } catch (e) {
        //     print('Validation error: $e');
        //     // Don't fail the entire loading process due to validation error
        //     state = state.copyWith(
        //       error: 'Validation error: ${e.toString()}',
        //     );
        //   }
        // }
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to load available documents',
        );
      }
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
    }
  }

  void selectRoute(int routeNumber) {
    if (state.availableDocuments?.availableRoutes.contains(routeNumber) ?? false) {
      state = state.copyWith(
        selectedRoute: routeNumber,
        nominations: [],
        lastValidation: null,
        error: null,
      );
    }
  }

  void addDocumentNomination(DocumentNomination nomination) {
    try {
      print('Adding nomination: documentTypeId=${nomination.documentTypeId} (${nomination.documentTypeId.runtimeType}), confirmsAddress=${nomination.confirmsAddress} (${nomination.confirmsAddress.runtimeType})');
      print('Document data: ${nomination.documentData}');

      final updatedNominations = List<DocumentNomination>.from(state.nominations);

      final existingIndex = updatedNominations.indexWhere(
        (nom) => nom.documentTypeId == nomination.documentTypeId,
      );

      if (existingIndex >= 0) {
        print('Updating existing nomination at index $existingIndex');
        updatedNominations[existingIndex] = nomination;
      } else {
        print('Adding new nomination');
        updatedNominations.add(nomination);
      }

      print('Total nominations after update: ${updatedNominations.length}');

      state = state.copyWith(
        nominations: updatedNominations,
        error: null,
      );

      print('State updated successfully');
    } catch (e, stackTrace) {
      print('Error in addDocumentNomination: $e');
      print('Stack trace: $stackTrace');
      state = state.copyWith(
        error: 'Failed to add nomination: ${e.toString()}',
      );
    }
  }

  void removeDocumentNomination(int documentTypeId) {
    final updatedNominations = state.nominations
        .where((nom) => nom.documentTypeId != documentTypeId)
        .toList();
    
    state = state.copyWith(
      nominations: updatedNominations,
      error: null,
    );
  }

  void updateDocumentData(int documentTypeId, Map<String, dynamic> data) {
    final nomination = state.getNominationForDocument(documentTypeId);
    if (nomination != null) {
      final updatedNomination = nomination.copyWith(documentData: data);
      addDocumentNomination(updatedNomination);
    }
  }

  void updateAddressConfirmation(int documentTypeId, bool confirmsAddress) {
    final nomination = state.getNominationForDocument(documentTypeId);
    if (nomination != null) {
      final updatedNomination = nomination.copyWith(confirmsAddress: confirmsAddress);
      addDocumentNomination(updatedNomination);
    }
  }

  Future<void> validateNominations(String applicationId) async {
    print('🔍 validateNominations called with applicationId: $applicationId');
    print('📋 Current nominations count: ${state.nominations.length}');

    // Print stack trace to see WHO called this method
    print('📍 Validation stack trace:');
    print(StackTrace.current);

    if (state.nominations.isEmpty) {
      print('❌ No nominations to validate');
      return;
    }

    print('🔄 Setting isValidating to true');
    state = state.copyWith(isValidating: true, error: null);

    try {
      print('📝 Creating validation request with route ${state.selectedRoute}');
      final request = DocumentNominationRequest(
        routeNumber: state.selectedRoute,
        nominatedDocuments: state.nominations,
      );

      print('🌐 Calling repository.validateDocumentNominations...');
      final response = await _repository.validateDocumentNominations(applicationId, request);

      print('📥 Validation response received: success=${response.success}');

      if (response.success) {
        print('✅ Validation successful');
        state = state.copyWith(
          isValidating: false,
          lastValidation: response.data,
        );
      } else {
        print('❌ Validation failed');
        state = state.copyWith(
          isValidating: false,
          error: 'Validation failed',
        );
      }
    } catch (error, stackTrace) {
      print('💥 Exception in validateNominations: $error');
      print('💥 Validation exception stack trace: $stackTrace');

      state = state.copyWith(
        isValidating: false,
        error: 'Failed to validate document nominations: ${error.toString()}',
      );
    }
  }

  Future<void> _validateCurrentNominations(String applicationId) async {
    if (state.nominations.isNotEmpty) {
      await validateNominations(applicationId);
    }
  }

  Future<bool> submitNominations(String applicationId) async {
    print('🚀 submitNominations called with applicationId: $applicationId');
    print('📋 Current nominations count: ${state.nominations.length}');

    // Print stack trace to see WHO called this method
    print('📍 Stack trace:');
    print(StackTrace.current);

    if (state.nominations.isEmpty) {
      print('❌ No nominations to submit');
      return false;
    }

    print('🔄 Setting isSubmitting to true');
    state = state.copyWith(isSubmitting: true, error: null);

    try {
      print('📝 Creating request with route ${state.selectedRoute}');
      for (int i = 0; i < state.nominations.length; i++) {
        final nom = state.nominations[i];
        print('📄 Nomination $i: documentTypeId=${nom.documentTypeId}, confirmsAddress=${nom.confirmsAddress}');
        print('📄 Document data: ${nom.documentData}');
      }

      final request = DocumentNominationRequest(
        routeNumber: state.selectedRoute,
        nominatedDocuments: state.nominations,
      );

      print('🌐 Calling repository.submitDocumentNominations...');
      final response = await _repository.submitDocumentNominations(applicationId, request);

      print('📥 Response received: success=${response.success}');
      print('📥 Response data: ${response.data}');

      // Check both HTTP success and validation result
      final isValidationSuccessful = response.success &&
                                   response.data.validationResult.isValid;

      print('✅ Validation successful: $isValidationSuccessful');
      print('✅ HTTP success: ${response.success}');
      print('✅ Validation result isValid: ${response.data.validationResult.isValid}');
      print('✅ Validation errors: ${response.data.validationResult.errors}');

      if (isValidationSuccessful) {
        print('🎉 Submission successful!');

        // Upload files if any are stored
        await _uploadStoredFiles(applicationId);

        state = state.copyWith(
          isSubmitting: false,
          successMessage: 'Document nominations submitted successfully',
          lastValidation: response.data,
        );
        return true;
      } else {
        // Get specific error message from validation
        final errorMessage = response.data.validationResult.errors.isNotEmpty
            ? response.data.validationResult.errors.first
            : 'Document validation failed';

        print('❌ Submission failed: $errorMessage');
        print('❌ All validation errors: ${response.data.validationResult.errors}');

        state = state.copyWith(
          isSubmitting: false,
          error: errorMessage,
          lastValidation: response.data,
        );
        return false;
      }
    } catch (error, stackTrace) {
      print('💥 Exception in submitNominations: $error');
      print('💥 Exception stack trace: $stackTrace');

      state = state.copyWith(
        isSubmitting: false,
        error: 'Failed to submit nominations: ${error.toString()}',
      );
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void clearSuccessMessage() {
    state = state.copyWith(successMessage: null);
  }

  void storeUploadedFile(int documentTypeId, File file) {
    final updatedFiles = Map<int, File>.from(state.uploadedFiles);
    updatedFiles[documentTypeId] = file;
    state = state.copyWith(uploadedFiles: updatedFiles);
  }

  void removeUploadedFile(int documentTypeId) {
    final updatedFiles = Map<int, File>.from(state.uploadedFiles);
    updatedFiles.remove(documentTypeId);
    state = state.copyWith(uploadedFiles: updatedFiles);
  }

  File? getUploadedFile(int documentTypeId) {
    return state.uploadedFiles[documentTypeId];
  }

  bool hasUploadedFile(int documentTypeId) {
    return state.uploadedFiles.containsKey(documentTypeId);
  }

  Future<void> _uploadStoredFiles(String applicationId) async {
    if (state.uploadedFiles.isEmpty) {
      print('📁 No files to upload');
      return;
    }

    print('📁 Uploading ${state.uploadedFiles.length} stored files...');

    try {
      // Get authentication token
      if (_authRepository == null) {
        print('❌ No auth repository available for file upload');
        return;
      }

      final token = await _authRepository!.getToken();
      if (token == null) {
        print('❌ No authentication token available for file upload');
        return;
      }

      // Create upload service
      final uploadService = DocumentUploadApiService(Dio());

      // Upload each file
      for (final entry in state.uploadedFiles.entries) {
        final documentTypeId = entry.key;
        final file = entry.value;

        try {
          print('📤 Uploading file for document type: $documentTypeId');

          // Find the nomination with this documentTypeId to get the actual nomination ID
          final nomination = state.nominations.firstWhere(
            (nom) => nom.documentTypeId == documentTypeId,
            orElse: () => throw Exception('Nomination not found for document type $documentTypeId'),
          );

          if (nomination.nominationId == null) {
            print('❌ No nomination ID available for document $documentTypeId');
            continue;
          }

          final result = await uploadService.uploadDocumentFile(
            token: token,
            applicationId: applicationId,
            nominationId: nomination.nominationId.toString(),
            file: file,
          );

          print('✅ File uploaded successfully for document $documentTypeId (nomination ${nomination.nominationId}): $result');
        } catch (error) {
          print('❌ Failed to upload file for document $documentTypeId: $error');
          // Continue with other files even if one fails
        }
      }

      print('📁 File upload process completed');
    } catch (error) {
      print('❌ Error during file upload process: $error');
    }
  }

  Future<void> _loadEntityOptionsFromCurrentUser() async {
    try {
      // Temporary: Skip auth calls if auth repository is null
      if (_authRepository == null) {
        _setDefaultEntityOptions();
        return;
      }

      // Get authentication token
      final token = await _authRepository!.getToken();
      if (token == null) {
        _setDefaultEntityOptions();
        return;
      }

      // Get current user to access their entities
      final userResult = await _authRepository!.getCurrentUser();

      if (userResult.isRight) {
        final authResult = userResult.right;
        if (authResult?.entities != null && authResult!.entities!.isNotEmpty) {
          // Use the first entity ID from the user's entities
          final entityId = authResult.entities!.first.id;
          if (entityId != null) {
            final options = await _entityOptionsService.getDocumentNominationOptions(entityId, token: token);
            state = state.copyWith(entityOptions: options);
            return;
          }
        }
      }

      // Fallback to defaults if no entity found or error occurred
      _setDefaultEntityOptions();
    } catch (error) {
      // If entity options fail to load, continue with defaults
      _setDefaultEntityOptions();
    }
  }

  void _setDefaultEntityOptions() {
    state = state.copyWith(entityOptions: {
      'allow_aidoc_scanner': false,
      'require_document_verification': true,
      'allow_manual_document_entry': true,
      'enable_document_upload': true,
    });
  }

  Future<ApplicantDetailsData?> _loadApplicantData(String applicationId) async {
    try {
      // Temporary: Skip auth calls if auth repository is null
      if (_authRepository == null) {
        return null;
      }

      // Get authentication token
      final token = await _authRepository!.getToken();

      if (token == null) {
        return null;
      }

      // Load DBS form data using the application ID
      final response = await DBSApiService.checkFormCompletionStatus(
        int.parse(applicationId),
        token: token,
      );

      if (response['success'] == true) {
        final data = response['data'];
        final formData = data['form_data'] as Map<String, dynamic>?;

        if (formData != null) {
          // Extract applicant details from the flat form data structure
          final forename = formData['ApplicantDetails::FORENAME'] as String? ?? '';
          final surname = formData['ApplicantDetails::PRESENT_SURNAME'] as String? ?? '';
          final dateOfBirth = formData['ApplicantDetails::DATE_OF_BIRTH'] as String? ?? '';
          final postcode = formData['CurrentAddress::POSTCODE'] as String? ?? '';

          // Create ApplicantDetailsData from the form data
          final applicantData = ApplicantDetailsData(
            title: formData['ApplicantDetails::TITLE'] as String? ?? '',
            forename: forename,
            middlenames: [],
            presentSurname: surname,
            dateOfBirth: dateOfBirth,
            gender: formData['ApplicantDetails::GENDER'] as String? ?? '',
            niNumber: formData['ApplicantDetails::NI_NUMBER'] as String? ?? '',
            email: '',
            contactNumber: formData['AdditionalApplicantDetails::CONTACT_NUMBER'] as String? ?? '',
            currentAddress: CurrentAddressData(
              addressLine1: formData['CurrentAddress::ADDRESS_LINE1'] as String? ?? '',
              addressLine2: formData['CurrentAddress::ADDRESS_LINE2'] as String? ?? '',
              addressTown: formData['CurrentAddress::ADDRESS_TOWN'] as String? ?? '',
              addressCounty: formData['CurrentAddress::ADDRESS_COUNTY'] as String? ?? '',
              postcode: postcode,
              countryCode: formData['CurrentAddress::COUNTRY_CODE'] as String? ?? 'GB',
              residentFromGyearMonth: formData['CurrentAddress::RESIDENT_FROM_YEAR_MONTH'] as String? ?? '',
            ),
            previousAddresses: [],
            additionalApplicantDetails: AdditionalApplicantDetailsData.empty(),
            applicantIdentityDetails: ApplicantIdentityDetailsData.empty(),
          );

          return applicantData;
        } else {
        }
      } else {
      }
    } catch (e) {
    }
    return null;
  }

  void reset() {
    state = DocumentNominationState();
  }

  /// Check if a field name indicates it's a date field
  bool _isDateField(String fieldName) {
    final dateFieldNames = [
      'expiry_date',
      'statement_date',
      'issue_date',
      'date_of_birth',
      'valid_from',
      'valid_to',
      'created_date',
      'updated_date',
    ];
    return dateFieldNames.contains(fieldName.toLowerCase());
  }

  /// Check if a string is in the old DD/MM/YYYY format
  bool _isOldDateFormat(String value) {
    // Check if it matches DD/MM/YYYY pattern
    final regex = RegExp(r'^\d{1,2}/\d{1,2}/\d{4}$');
    return regex.hasMatch(value);
  }
}
