<?php

declare(strict_types=1);

namespace App\Modules\Documents\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Modules\Applications\Models\Application;
use App\Modules\Auth\Models\PortalUser;

class DocumentFile extends Model
{
    use HasFactory;

    protected $table = 'document_files';

    protected $fillable = [
        'document_nomination_id',
        'original_filename',
        's3_key',
        's3_bucket',
        'file_size',
        'mime_type',
        'file_hash',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'access_count' => 'integer',
        'uploaded_at' => 'datetime',
        'transition_to_ia_at' => 'datetime',
        'transition_to_glacier_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'archived_at' => 'datetime',
        'deleted_at' => 'datetime',
        'retention_until' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function application(): BelongsTo
    {
        return $this->belongsTo(Application::class);
    }

    public function docDafData(): BelongsTo
    {
        return $this->belongsTo(DocDafData::class, 'doc_daf_data_id');
    }

    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(PortalUser::class, 'uploaded_by');
    }

    public function documentNomination(): BelongsTo
    {
        return $this->belongsTo(\App\Modules\Documents\Models\DocumentNomination::class, 'document_nomination_id');
    }

    /**
     * Get the full S3 URL for the file
     */
    public function getS3Url(): string
    {
        return "s3://{$this->s3_bucket}/{$this->s3_key}";
    }

    /**
     * Get human readable file size
     */
    public function getFormattedFileSize(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file is an image
     */
    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if file is a PDF
     */
    public function isPdf(): bool
    {
        return $this->mime_type === 'application/pdf';
    }
}
