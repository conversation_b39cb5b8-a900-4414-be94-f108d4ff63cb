<?php

declare(strict_types=1);

namespace App\Services;

use App\Modules\Documents\Models\DocumentFile;
use App\Modules\Documents\Models\DocumentNomination;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class SecureDocumentStorageService
{
    private const ALLOWED_MIME_TYPES = [
        'image/jpeg',
        'image/png', 
        'image/tiff',
        'image/bmp',
        'application/pdf'
    ];

    private const MAX_FILE_SIZE = 10485760; // 10MB
    private const STORAGE_DISK = 'secure_documents';

    public function storeDocumentFile(
        UploadedFile $file,
        DocumentNomination $documentNomination,
        int $uploadedBy
    ): DocumentFile {
        Log::info('📁 Starting document file storage', [
            'nomination_id' => $documentNomination->id,
            'application_id' => $documentNomination->application_id,
            'document_type' => $documentNomination->document_type_key,
            'uploaded_by' => $uploadedBy,
            'file_name' => $file->getClientOriginalName(),
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType()
        ]);

        $this->validateFile($file);
        Log::info('✅ File validation passed');

        DB::beginTransaction();

        try {
            // Generate secure S3 key
            $s3Key = $this->generateS3Key($documentNomination, $file);
            Log::info('🔑 Generated S3 key', ['s3_key' => $s3Key]);

            // Calculate file hash for integrity
            $fileHash = hash_file('sha256', $file->getRealPath());
            Log::info('🔐 File hash calculated', ['hash' => substr($fileHash, 0, 16) . '...']);

            // Store file to S3 with encryption
            Log::info('☁️ Uploading to S3', [
                'disk' => self::STORAGE_DISK,
                'bucket' => config('filesystems.disks.secure_documents.bucket'),
                's3_key' => $s3Key,
                'directory' => dirname($s3Key),
                'filename' => basename($s3Key)
            ]);

            $s3Response = Storage::disk(self::STORAGE_DISK)->putFileAs(
                dirname($s3Key),
                $file,
                basename($s3Key),
                [
                    'ServerSideEncryption' => 'AES256',
                    'StorageClass' => 'STANDARD',
                    'Metadata' => [
                        'application-id' => (string)$documentNomination->application_id,
                        'document-nomination-id' => (string)$documentNomination->id,
                        'document-type' => $documentNomination->document_type_key,
                        'uploaded-by' => (string)$uploadedBy,
                        'original-filename' => $file->getClientOriginalName(),
                        'file-hash' => $fileHash,
                        'upload-timestamp' => now()->toISOString(),
                        'system' => 'dbs-document-system',
                        'compliance' => 'gdpr'
                    ]
                ]
            );

            if (!$s3Response) {
                Log::error('❌ S3 upload failed', ['s3_key' => $s3Key, 'response' => $s3Response]);
                throw new \Exception('Failed to store file to S3');
            }

            Log::info('✅ S3 upload successful', ['s3_key' => $s3Key, 'response' => $s3Response]);

            // Create database record
            Log::info('💾 Creating database record');

            $documentFile = DocumentFile::create([
                'document_nomination_id' => $documentNomination->id,
                'original_filename' => $file->getClientOriginalName(),
                's3_key' => $s3Key,
                's3_bucket' => config('filesystems.disks.secure_documents.bucket'),
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'file_hash' => $fileHash,
                'uploaded_by' => $uploadedBy
            ]);

            Log::info('✅ Database record created', [
                'file_id' => $documentFile->id,
                'nomination_id' => $documentNomination->id
            ]);

            // Load the relationship before logging
            $documentFile->load('documentNomination');

            // Log the upload for centralized logging
            $this->logFileOperation('upload', $documentFile, $uploadedBy, [
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                's3_key' => $s3Key
            ]);

            DB::commit();

            return $documentFile;

        } catch (\Exception $e) {
            DB::rollBack();
            
            // Clean up S3 file if database insert failed
            if (isset($s3Key)) {
                try {
                    Storage::disk(self::STORAGE_DISK)->delete($s3Key);
                } catch (\Exception $cleanupException) {
                    Log::error('Failed to cleanup S3 file after database error', [
                        's3_key' => $s3Key,
                        'cleanup_error' => $cleanupException->getMessage()
                    ]);
                }
            }

            Log::error('Document file storage failed', [
                'document_nomination_id' => $documentNomination->id,
                'application_id' => $documentNomination->application_id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    public function getSecureUrl(DocumentFile $documentFile, int $userId, string $accessReason = 'view'): string
    {
        // Check access permissions
        $this->checkAccessPermissions($documentFile, $userId);

        // Generate presigned URL (5 minutes expiry)
        $url = $documentFile->getSecureUrl(5);

        // Log access for centralized logging
        $this->logFileOperation('access', $documentFile, $userId, [
            'access_reason' => $accessReason,
            'url_expires_at' => now()->addMinutes(5)->toISOString()
        ]);

        return $url;
    }

    public function deleteDocumentFile(DocumentFile $documentFile, int $userId, string $reason = 'user_request'): bool
    {
        try {
            // Log deletion before actual deletion
            $this->logFileOperation('delete', $documentFile, $userId, [
                'deletion_reason' => $reason,
                'file_size' => $documentFile->file_size,
                's3_key' => $documentFile->s3_key
            ]);

            // Delete the file (this also removes from S3)
            return $documentFile->delete();

        } catch (\Exception $e) {
            Log::error('Document file deletion failed', [
                'document_file_id' => $documentFile->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    private function validateFile(UploadedFile $file): void
    {
        if (!$file->isValid()) {
            throw new \InvalidArgumentException('Invalid file upload');
        }

        if ($file->getSize() > self::MAX_FILE_SIZE) {
            throw new \InvalidArgumentException('File size exceeds maximum allowed size (10MB)');
        }

        if (!in_array($file->getMimeType(), self::ALLOWED_MIME_TYPES)) {
            throw new \InvalidArgumentException('File type not allowed. Allowed types: ' . implode(', ', self::ALLOWED_MIME_TYPES));
        }
    }

    private function generateS3Key(DocumentNomination $documentNomination, UploadedFile $file): string
    {
        $uuid = Str::uuid();
        $extension = $file->getClientOriginalExtension();
        $filename = "{$uuid}.{$extension}";
        
        return "applications/{$documentNomination->application_id}/documents/{$documentNomination->document_type_key}/{$filename}";
    }

    private function checkAccessPermissions(DocumentFile $documentFile, int $userId): void
    {
        // Check if user has access to this application
        $hasAccess = DB::table('applications')
            ->join('portal_users', 'applications.client_id', '=', 'portal_users.client_id')
            ->where('applications.id', $documentFile->documentNomination->application_id)
            ->where('portal_users.id', $userId)
            ->exists();

        if (!$hasAccess) {
            throw new \Exception('Access denied to document file');
        }
    }

    private function logFileOperation(string $operation, DocumentFile $documentFile, int $userId, array $context = []): void
    {
        // This will be sent to centralized logging system (ELK, Splunk, etc.)
        Log::info("Document file {$operation}", [
            'event_type' => "document_file_{$operation}",
            'timestamp' => now()->toISOString(),
            'document_file_id' => $documentFile->id,
            'document_nomination_id' => $documentFile->document_nomination_id,
            'application_id' => $documentFile->documentNomination->application_id,
            'user_id' => $userId,
            'user_type' => 'portal_user',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
            'request_id' => request()->header('X-Request-ID'),
            's3_key' => $documentFile->s3_key,
            's3_bucket' => $documentFile->s3_bucket,
            'file_hash' => $documentFile->file_hash,
            'compliance_context' => [
                'gdpr_basis' => 'legitimate_interest',
                'data_classification' => 'restricted',
                'retention_policy' => 'dbs_document_retention'
            ],
            'context' => $context
        ]);
    }
}
